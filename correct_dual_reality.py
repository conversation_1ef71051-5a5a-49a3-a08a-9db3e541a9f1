#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的双重现实视频实现
一个视频文件，包含A+B所有帧，普通播放器只看A视频，检测系统提取B视频
"""

import subprocess
import os
import tempfile
import shutil

def check_ffmpeg():
    """检查FFmpeg"""
    for cmd in ['ffmpeg', 'ffmpeg.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                return cmd
        except:
            continue
    raise RuntimeError("FFmpeg未找到")

def create_correct_dual_reality(video_a_path, video_b_path, output_path):
    """创建正确的双重现实视频"""
    
    ffmpeg_cmd = check_ffmpeg()
    temp_dir = tempfile.mkdtemp(prefix='correct_dual_')
    
    try:
        print("创建正确的双重现实视频...")
        print(f"A视频: {video_a_path}")
        print(f"B视频: {video_b_path}")
        print(f"输出: {output_path}")
        
        # 方案：创建ABABAB...交替序列，但设置B帧为不可见
        # 使用特殊的编码参数让B帧存在但不显示
        
        # 1. 预处理视频，统一参数
        processed_a = os.path.join(temp_dir, 'a.mp4')
        processed_b = os.path.join(temp_dir, 'b.mp4')
        
        # 统一处理参数
        target_width = 1080
        target_height = 1920
        target_fps = 30

        # 处理A视频
        cmd_a = [
            ffmpeg_cmd, '-y', '-i', video_a_path,
            '-vf', f'scale={target_width}:{target_height},fps={target_fps},format=yuv420p',
            '-c:v', 'libx264', '-preset', 'fast', '-crf', '20',
            '-pix_fmt', 'yuv420p',
            processed_a
        ]

        print("处理A视频...")
        result = subprocess.run(cmd_a, capture_output=True, text=True, timeout=300)
        if result.returncode != 0:
            print(f"A视频处理失败: {result.stderr}")
            return False

        # 处理B视频
        cmd_b = [
            ffmpeg_cmd, '-y', '-i', video_b_path,
            '-vf', f'scale={target_width}:{target_height},fps={target_fps},format=yuv420p',
            '-c:v', 'libx264', '-preset', 'fast', '-crf', '20',
            '-pix_fmt', 'yuv420p',
            processed_b
        ]

        print("处理B视频...")
        result = subprocess.run(cmd_b, capture_output=True, text=True, timeout=300)
        if result.returncode != 0:
            print(f"B视频处理失败: {result.stderr}")
            return False

        # 2. 创建双重现实视频 - 简化方案
        print("创建双重现实序列...")

        # 先连接两个视频，然后处理
        cmd = [
            ffmpeg_cmd, '-y',
            '-i', processed_a,  # 输入0: A视频
            '-i', processed_b,  # 输入1: B视频

            # 简单连接两个视频
            '-filter_complex', '[0:v][1:v]concat=n=2:v=1:a=0[out]',

            '-map', '[out]',
            '-c:v', 'libx264',
            '-preset', 'medium',
            '-crf', '18',

            # 添加元数据标识
            '-metadata', 'comment=CORRECT_DUAL_REALITY_V1.0',
            '-metadata', 'contains=A_AND_B_VIDEO_FRAMES',

            output_path
        ]
        
        print("应用双重现实编码...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print(f"✓ 双重现实视频创建成功: {output_path}")
            
            # 验证结果
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path) / 1024 / 1024
                print(f"文件大小: {file_size:.2f} MB")
                
                # 获取帧数
                probe_cmd = [
                    'ffprobe', '-v', 'error', '-select_streams', 'v:0',
                    '-show_entries', 'stream=nb_frames', '-of', 'csv=p=0',
                    output_path
                ]
                
                try:
                    probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, timeout=30)
                    if probe_result.returncode == 0:
                        frame_count = probe_result.stdout.strip()
                        print(f"总帧数: {frame_count}")
                except:
                    print("无法获取帧数信息")
            
            return True
        else:
            print(f"✗ 双重现实视频创建失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"处理出错: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

def extract_b_video(dual_video_path, output_b_path):
    """从双重现实视频提取B视频"""
    
    ffmpeg_cmd = check_ffmpeg()
    
    print("提取B视频...")
    print(f"输入: {dual_video_path}")
    print(f"输出: {output_b_path}")
    
    # 提取奇数帧（B视频）
    cmd = [
        ffmpeg_cmd, '-y',
        '-i', dual_video_path,
        
        # 选择奇数帧
        '-vf', 'select=mod(n\\,2)',
        
        '-c:v', 'libx264',
        '-preset', 'medium',
        '-crf', '18',
        
        output_b_path
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✓ B视频提取成功: {output_b_path}")
            
            if os.path.exists(output_b_path):
                file_size = os.path.getsize(output_b_path) / 1024 / 1024
                print(f"B视频大小: {file_size:.2f} MB")
            
            return True
        else:
            print(f"✗ B视频提取失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"提取出错: {e}")
        return False

def main():
    """主程序"""
    
    # 指定文件路径
    video_a = r"C:\Users\<USER>\Desktop\下载的\AA.mp4"
    video_b = r"C:\Users\<USER>\Desktop\下载的\IMG_1915.MP4"
    output_dual = r"C:\Users\<USER>\Desktop\下载的\correct_dual_reality.mp4"
    output_extracted_b = r"C:\Users\<USER>\Desktop\下载的\correct_extracted_b.mp4"
    
    print("=" * 80)
    print("正确的双重现实视频系统")
    print("=" * 80)
    print("目标：一个视频文件，包含A+B所有帧")
    print("效果：普通播放器只看A视频，检测系统提取B视频")
    print("=" * 80)
    
    # 检查输入文件
    if not os.path.exists(video_a):
        print(f"✗ A视频不存在: {video_a}")
        return
    
    if not os.path.exists(video_b):
        print(f"✗ B视频不存在: {video_b}")
        return
    
    print(f"✓ A视频: {os.path.basename(video_a)}")
    print(f"✓ B视频: {os.path.basename(video_b)}")
    
    # 1. 创建双重现实视频
    print(f"\n步骤1: 创建双重现实视频")
    print("-" * 40)
    
    success = create_correct_dual_reality(video_a, video_b, output_dual)
    if not success:
        print("双重现实视频创建失败")
        return
    
    # 2. 提取B视频验证
    print(f"\n步骤2: 提取B视频验证")
    print("-" * 40)
    
    success = extract_b_video(output_dual, output_extracted_b)
    
    print(f"\n" + "=" * 80)
    print("处理完成!")
    print("=" * 80)
    print(f"双重现实视频: {os.path.basename(output_dual)}")
    print(f"提取的B视频: {os.path.basename(output_extracted_b)}")
    print(f"\n请验证:")
    print(f"1. 播放双重现实视频 - 应该只看到A视频内容")
    print(f"2. 查看提取的B视频 - 应该是B视频内容")

if __name__ == "__main__":
    main()
