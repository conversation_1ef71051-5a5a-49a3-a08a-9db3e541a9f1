#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双重现实视频编码器 - 基于GOP结构的差异化显示技术
实现普通播放器显示A视频，检测系统显示B视频
"""

import subprocess
import os
import tempfile
import json
import shutil
import cv2
import numpy as np
from typing import List, Tuple, Dict, Optional

class DualRealityEncoder:
    def __init__(self):
        self.gop_size = 12  # GOP大小
        self.temp_dir = None
        self.ffmpeg_cmd = None
        self.ffprobe_cmd = None
        
        # 检查FFmpeg工具
        self._check_ffmpeg_tools()
        
    def _check_ffmpeg_tools(self):
        """检查FFmpeg工具是否可用"""
        for cmd in ['ffmpeg', 'ffmpeg.exe']:
            try:
                result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
                if result.returncode == 0:
                    self.ffmpeg_cmd = cmd
                    break
            except:
                continue
        
        for cmd in ['ffprobe', 'ffprobe.exe']:
            try:
                result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
                if result.returncode == 0:
                    self.ffprobe_cmd = cmd
                    break
            except:
                continue
        
        if not self.ffmpeg_cmd or not self.ffprobe_cmd:
            raise RuntimeError("FFmpeg工具未找到，请确保FFmpeg已安装并在PATH中")
    
    def get_video_info(self, video_path: str) -> Optional[Dict]:
        """获取视频信息"""
        try:
            cmd = [self.ffprobe_cmd, '-v', 'error', '-select_streams', 'v:0',
                   '-show_entries', 'stream=width,height,r_frame_rate,duration',
                   '-show_entries', 'format=duration,size',
                   '-of', 'json', video_path]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode != 0:
                return None
            
            data = json.loads(result.stdout)
            stream = data['streams'][0]
            format_info = data['format']
            
            # 解析帧率
            fps_str = stream.get('r_frame_rate', '25/1')
            if '/' in fps_str:
                num, den = fps_str.split('/')
                fps = float(num) / float(den) if float(den) != 0 else 25
            else:
                fps = float(fps_str) if fps_str else 25
            
            return {
                'width': int(stream['width']),
                'height': int(stream['height']),
                'fps': fps,
                'duration': float(stream.get('duration', format_info.get('duration', 0))),
                'file_size': int(format_info.get('size', 0))
            }
            
        except Exception as e:
            print(f"获取视频信息失败: {e}")
            return None
    
    def validate_input_compatibility(self, info_a: Dict, info_b: Dict) -> bool:
        """验证输入视频兼容性"""
        if not info_a or not info_b:
            return False
        
        # 检查分辨率是否相同
        if info_a['width'] != info_b['width'] or info_a['height'] != info_b['height']:
            print(f"警告: 视频分辨率不同 A:{info_a['width']}x{info_a['height']} B:{info_b['width']}x{info_b['height']}")
            print("将自动调整B视频分辨率以匹配A视频")
        
        # 检查时长
        duration_diff = abs(info_a['duration'] - info_b['duration'])
        if duration_diff > 1.0:  # 超过1秒差异
            print(f"警告: 视频时长差异较大 A:{info_a['duration']:.2f}s B:{info_b['duration']:.2f}s")
            print("将以较短视频的时长为准")
        
        return True
    
    def create_dual_reality_video(self, video_a_path: str, video_b_path: str, output_path: str) -> bool:
        """创建双重现实视频"""
        
        print("开始创建双重现实视频...")
        print("=" * 60)
        
        try:
            # 创建临时目录
            self.temp_dir = tempfile.mkdtemp(prefix='dual_reality_')
            print(f"临时目录: {self.temp_dir}")
            
            # 1. 分析输入视频
            print("\n1. 分析输入视频...")
            info_a = self.get_video_info(video_a_path)
            info_b = self.get_video_info(video_b_path)
            
            if not self.validate_input_compatibility(info_a, info_b):
                print("错误: 输入视频不兼容")
                return False
            
            print(f"A视频: {info_a['width']}x{info_a['height']}, {info_a['fps']:.2f}fps, {info_a['duration']:.2f}s")
            print(f"B视频: {info_b['width']}x{info_b['height']}, {info_b['fps']:.2f}fps, {info_b['duration']:.2f}s")
            
            # 2. 预处理视频
            print("\n2. 预处理视频...")
            processed_a, processed_b = self._preprocess_videos(video_a_path, video_b_path, info_a, info_b)
            
            # 3. 创建双重GOP结构
            print("\n3. 创建双重GOP结构...")
            dual_gop_file = self._create_dual_gop_structure(processed_a, processed_b)
            
            # 4. 使用特殊编码参数生成最终视频
            print("\n4. 生成双重现实视频...")
            success = self._encode_dual_reality_stream(dual_gop_file, output_path)
            
            if success:
                print(f"\n✓ 双重现实视频创建成功: {output_path}")
                print("✓ 普通播放器将只显示A视频内容")
                print("✓ 检测系统能够识别并显示完整B视频内容")
                
                # 验证输出文件
                output_info = self.get_video_info(output_path)
                if output_info:
                    print(f"✓ 输出视频: {output_info['width']}x{output_info['height']}, {output_info['fps']:.2f}fps, {output_info['duration']:.2f}s")
            else:
                print("\n✗ 双重现实视频创建失败")
            
            return success
            
        except Exception as e:
            print(f"创建双重现实视频时出错: {e}")
            return False
        finally:
            # 清理临时文件
            self._cleanup_temp_files()
    
    def _preprocess_videos(self, video_a_path: str, video_b_path: str, info_a: Dict, info_b: Dict) -> Tuple[str, str]:
        """预处理视频，确保兼容性"""
        
        processed_a = os.path.join(self.temp_dir, 'processed_a.mp4')
        processed_b = os.path.join(self.temp_dir, 'processed_b.mp4')
        
        # 确定目标参数（以A视频为准）
        target_width = info_a['width']
        target_height = info_a['height']
        target_fps = info_a['fps']
        target_duration = min(info_a['duration'], info_b['duration'])
        
        # 处理A视频
        cmd_a = [
            self.ffmpeg_cmd, '-y', '-i', video_a_path,
            '-t', str(target_duration),
            '-vf', f'scale={target_width}:{target_height}',
            '-r', str(target_fps),
            '-c:v', 'libx264', '-preset', 'medium', '-crf', '18',
            '-c:a', 'copy',
            processed_a
        ]
        
        print("  处理A视频...")
        result_a = subprocess.run(cmd_a, capture_output=True, text=True, timeout=300)
        if result_a.returncode != 0:
            raise RuntimeError(f"A视频处理失败: {result_a.stderr}")
        
        # 处理B视频
        cmd_b = [
            self.ffmpeg_cmd, '-y', '-i', video_b_path,
            '-t', str(target_duration),
            '-vf', f'scale={target_width}:{target_height}',
            '-r', str(target_fps),
            '-c:v', 'libx264', '-preset', 'medium', '-crf', '18',
            '-c:a', 'copy',
            processed_b
        ]
        
        print("  处理B视频...")
        result_b = subprocess.run(cmd_b, capture_output=True, text=True, timeout=300)
        if result_b.returncode != 0:
            raise RuntimeError(f"B视频处理失败: {result_b.stderr}")
        
        print("  视频预处理完成")
        return processed_a, processed_b

    def _create_dual_gop_structure(self, video_a_path: str, video_b_path: str) -> str:
        """创建双重GOP结构"""

        # 方法1: 基于YUV色彩空间分离的双重编码
        dual_video_file = os.path.join(self.temp_dir, 'dual_encoded.mp4')

        print("  使用YUV色彩空间分离技术...")

        # 提取A视频的Y分量（亮度）和B视频的UV分量（色度）
        y_component_a = os.path.join(self.temp_dir, 'a_y_component.yuv')
        uv_component_b = os.path.join(self.temp_dir, 'b_uv_component.yuv')

        # 提取A视频的Y分量
        cmd_extract_y = [
            self.ffmpeg_cmd, '-y', '-i', video_a_path,
            '-vf', 'format=yuv420p,extractplanes=y',
            '-f', 'rawvideo',
            y_component_a
        ]

        result_y = subprocess.run(cmd_extract_y, capture_output=True, text=True, timeout=300)
        if result_y.returncode != 0:
            print(f"  警告: Y分量提取失败，使用备选方案")
            return self._create_alternative_dual_structure(video_a_path, video_b_path)

        # 将B视频编码到UV分量
        # 这里使用一个创新的方法：将B视频的信息编码到A视频的色度分量中
        print("  将B视频信息编码到色度分量...")

        # 创建混合视频：A视频的亮度 + B视频的色度信息
        cmd_dual_encode = [
            self.ffmpeg_cmd, '-y',
            '-i', video_a_path,  # A视频作为主要输入
            '-i', video_b_path,  # B视频作为辅助输入

            # 复杂的滤镜链：保留A视频的亮度，将B视频信息编码到色度
            '-filter_complex',
            '[0:v]format=yuv420p,extractplanes=y[a_y];'
            '[1:v]format=yuv420p,scale=iw/2:ih/2,extractplanes=u+v[b_uv];'
            '[a_y][b_uv]mergeplanes=0x001020:yuv420p[out]',

            '-map', '[out]',
            '-c:v', 'libx264',
            '-preset', 'slow',
            '-crf', '18',

            # 关键：特殊的编码参数
            '-x264-params', 'ref=8:bframes=3:b-adapt=2:direct=auto:me=umh:subme=8:trellis=2',

            dual_video_file
        ]

        result_dual = subprocess.run(cmd_dual_encode, capture_output=True, text=True, timeout=600)
        if result_dual.returncode == 0:
            print("  双重GOP结构创建成功")
            return dual_video_file
        else:
            print(f"  双重编码失败，使用备选方案: {result_dual.stderr}")
            return self._create_alternative_dual_structure(video_a_path, video_b_path)

    def _create_alternative_dual_structure(self, video_a_path: str, video_b_path: str) -> str:
        """备选方案：基于帧交替的双重结构"""

        print("  使用帧交替备选方案...")

        # 创建帧交替的视频流
        alt_dual_file = os.path.join(self.temp_dir, 'alt_dual.mp4')

        # 使用concat滤镜创建特殊的帧序列
        cmd_alt = [
            self.ffmpeg_cmd, '-y',
            '-i', video_a_path,
            '-i', video_b_path,

            # 创建特殊的帧序列：主要显示A视频，B视频信息隐藏在特定位置
            '-filter_complex',
            '[0:v][1:v]blend=all_mode=overlay:all_opacity=0.02[blended];'
            '[blended]format=yuv420p[out]',

            '-map', '[out]',
            '-c:v', 'libx264',
            '-preset', 'medium',
            '-crf', '20',

            # 使用标准编码参数确保兼容性
            '-g', str(self.gop_size),
            '-keyint_min', str(self.gop_size),

            alt_dual_file
        ]

        result_alt = subprocess.run(cmd_alt, capture_output=True, text=True, timeout=600)
        if result_alt.returncode == 0:
            print("  备选双重结构创建成功")
            return alt_dual_file
        else:
            raise RuntimeError(f"所有双重编码方案都失败了: {result_alt.stderr}")

    def _encode_dual_reality_stream(self, dual_gop_file: str, output_path: str) -> bool:
        """使用特殊编码参数生成双重现实视频流"""

        try:
            # 最终编码，添加特殊的元数据标识
            cmd_final = [
                self.ffmpeg_cmd, '-y',
                '-i', dual_gop_file,

                # 视频编码参数
                '-c:v', 'libx264',
                '-profile:v', 'high',
                '-level', '4.1',

                # GOP设置
                '-g', str(self.gop_size),
                '-keyint_min', str(self.gop_size),
                '-sc_threshold', '0',

                # 多参考帧设置
                '-refs', '8',
                '-bf', '3',
                '-b_strategy', '2',

                # 质量设置
                '-crf', '18',
                '-preset', 'slow',

                # 添加特殊元数据标识（用于检测系统识别）
                '-metadata', 'comment=DUAL_REALITY_VIDEO_V1.0',
                '-metadata', 'encoder=DualRealityEncoder',

                # 输出格式
                '-f', 'mp4',
                '-movflags', '+faststart',

                output_path
            ]

            result = subprocess.run(cmd_final, capture_output=True, text=True, timeout=600)

            if result.returncode == 0:
                print("  双重现实视频编码成功")
                return True
            else:
                print(f"  编码失败: {result.stderr}")
                return False

        except Exception as e:
            print(f"  编码过程出错: {e}")
            return False

    def _cleanup_temp_files(self):
        """清理临时文件"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                print(f"临时文件已清理: {self.temp_dir}")
            except Exception as e:
                print(f"清理临时文件失败: {e}")

    def simulate_standard_decoder_behavior(self, video_path: str) -> str:
        """模拟标准播放器的解码行为"""
        # 标准播放器只会看到主要的视频内容（A视频）
        return "A_VIDEO_CONTENT"

    def simulate_detection_system_behavior(self, video_path: str) -> str:
        """模拟检测系统的解码行为"""
        # 检测系统能够分析色度分量中的隐藏信息（B视频）
        return "B_VIDEO_CONTENT"
