;
;
;Bug: "Page_Begin", "Page_Middle" and "Page_End" sections are picked on lines 10, 11 and 12 regardless what is there. So it is better to leave them there.
;Bug: \r\n is not turned into a newline on "Page" entries.
;Bug: "Image" sections are not active, but should.
;
;
;
Page;(unused)\r\n
Page_Begin;<html><head><title>Media Info</title><style type="text/css">html,body,table{font-size:xx-small} html,body{margin:0} html,body{padding:0} table{empty-cells:show} td.Name{white-space:pre-wrap} </style></head><body><table class="body" width="100%" border="1" cellpadding="1" cellspacing="0" style="border:0px"><caption></caption><col><col span="2" align="right" style="white-space:nowrap"><thead><tr align="center"><th>File</th><th align="center">Size</th><th align="center">Time</th><th>Container</th><th>Video</th><th>Audio</th><th>Sub</th><th>Chaps</th></tr></thead><tbody>
Page_Middle;
Page_End;</tbody></table></body></html>
;
File;(unused)\r\n
File_Begin;<tr>
File_Middle;(unused)\r\n
File_End;</tr>
;
General;<td class="Name">%FileName%.%FileExtension%</td><td>%FileSize/String2%</td><td>%Duration/String%</td><td bgcolor="#DFDFDF">[%BitRate/String%][ %Format%]</td>$if(%Video_Format_List%,,<td bgcolor="white"></td>)
General_Begin;
General_Middle;(unused)\r\n
General_End;
;
Video;<b>#%StreamKindID%:</b>[%Width%x%Height%][ %Resolution%bits][ %FrameRate%fps][ %BitRate/String%][ %Format%]
Video_Begin;<td bgcolor="#FFBFBF">
Video_Middle;<br>
Video_End;</td>
;
Audio;<b>#%StreamKindID%:</b>[ %Channel(s)%ch][ %Resolution%bits][ %SamplingRate/String%][ %BitRate/String%][ %Format%]
Audio_Begin;<td bgcolor="#BFFFBF">
Audio_Middle;<br>
Audio_End;</td>
;
Text;<b>#%StreamKindID%:</b>%Format%[@%Language%]
Text_Begin;<td bgcolor="#BFBFFF">
Text_Middle;<br>
Text_End;</td>
;
Chapters;<b>#%StreamKindID%:</b>%Format%[@%Language%][ %Total% entries]
Chapters_Begin;<td bgcolor="#FFFFBF">
Chapters_Middle;<br>
Chapters_End;
;
Image;<b>#%StreamKindID%:</b>[%Width%x%Height%][ %Resolution%bits][ %StreamSize/String4%][ %Format%]
Image_Begin;<td bgcolor="#FFBFFF">
Image_Middle;<br>
Image_End;
;
Menu;<b>#%StreamKindID%:</b>[%Width%x%Height%][ %Resolution%bits][ %FrameRate/String%][ %BitRate/String%][ %Format%][ %Language%]
Menu_Begin;<td bgcolor="#BFFFFF">
Menu_Middle;<br>
Menu_End;</td>
;