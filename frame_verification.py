#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
帧级别验证工具 - 检查每一帧是否真正包含B视频数据
"""

import os
import subprocess
import tempfile
import shutil

def check_ffmpeg():
    """检查FFmpeg工具是否可用"""
    ffmpeg_cmd = None
    ffprobe_cmd = None
    
    for cmd in ['ffmpeg', 'ffmpeg.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                ffmpeg_cmd = cmd
                break
        except:
            continue
    
    for cmd in ['ffprobe', 'ffprobe.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                ffprobe_cmd = cmd
                break
        except:
            continue
    
    return ffmpeg_cmd, ffprobe_cmd

def extract_frames_for_analysis(video_path, output_dir, max_frames=10):
    """提取视频帧进行分析"""
    ffmpeg_cmd, _ = check_ffmpeg()
    if not ffmpeg_cmd:
        return False
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        # 提取前max_frames帧
        cmd = [ffmpeg_cmd, '-i', video_path, '-vframes', str(max_frames),
               f'{output_dir}/frame_%03d.png']
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        return result.returncode == 0
            
    except Exception as e:
        print(f"帧提取失败: {e}")
        return False

def get_frame_size(frame_path):
    """获取帧文件大小"""
    try:
        return os.path.getsize(frame_path)
    except:
        return 0

def analyze_frame_data(frame_path):
    """分析帧数据内容"""
    try:
        with open(frame_path, 'rb') as f:
            data = f.read()
        
        # 检查是否包含B视频标识符
        b_video_marker = b'BVID'
        has_b_marker = b_video_marker in data
        
        # 检查数据复杂度（简单的熵估算）
        unique_bytes = len(set(data))
        complexity = unique_bytes / 256.0  # 0-1之间的复杂度
        
        return {
            'size': len(data),
            'has_b_marker': has_b_marker,
            'complexity': complexity,
            'unique_bytes': unique_bytes
        }
        
    except Exception as e:
        print(f"帧分析失败: {e}")
        return None

def compare_videos_frame_by_frame(original_a, original_b, combined_video):
    """逐帧对比分析"""
    print("开始逐帧验证分析...")
    
    temp_dir = f"temp_verification_{int(time.time())}"
    
    try:
        # 提取原始A视频的帧
        print("提取原始A视频帧...")
        a_frames_dir = os.path.join(temp_dir, 'a_frames')
        if not extract_frames_for_analysis(original_a, a_frames_dir):
            print("A视频帧提取失败")
            return False
        
        # 提取原始B视频的帧
        print("提取原始B视频帧...")
        b_frames_dir = os.path.join(temp_dir, 'b_frames')
        if not extract_frames_for_analysis(original_b, b_frames_dir):
            print("B视频帧提取失败")
            return False
        
        # 提取合并视频的帧
        print("提取合并视频帧...")
        combined_frames_dir = os.path.join(temp_dir, 'combined_frames')
        if not extract_frames_for_analysis(combined_video, combined_frames_dir):
            print("合并视频帧提取失败")
            return False
        
        # 分析每一帧
        print("\n逐帧分析结果:")
        print("帧号 | A帧大小 | B帧大小 | 合并帧大小 | 期望最小 | 实际/期望 | B标识 | 复杂度")
        print("-" * 80)
        
        verification_passed = True
        
        for i in range(1, 11):  # 检查前10帧
            a_frame = os.path.join(a_frames_dir, f'frame_{i:03d}.png')
            b_frame = os.path.join(b_frames_dir, f'frame_{i:03d}.png')
            combined_frame = os.path.join(combined_frames_dir, f'frame_{i:03d}.png')
            
            if not all(os.path.exists(f) for f in [a_frame, b_frame, combined_frame]):
                continue
            
            a_size = get_frame_size(a_frame)
            b_size = get_frame_size(b_frame)
            combined_size = get_frame_size(combined_frame)
            
            # 分析合并帧数据
            combined_analysis = analyze_frame_data(combined_frame)
            
            # 期望的最小大小（应该至少包含A帧的大小）
            expected_min = a_size
            # 理想情况下应该接近A+B的大小
            expected_ideal = a_size + b_size
            
            ratio = combined_size / expected_ideal if expected_ideal > 0 else 0
            
            has_b = "✓" if combined_analysis and combined_analysis['has_b_marker'] else "✗"
            complexity = f"{combined_analysis['complexity']:.2f}" if combined_analysis else "N/A"
            
            print(f"{i:3d}  | {a_size:7d} | {b_size:7d} | {combined_size:9d} | {expected_min:7d} | {ratio:8.2f} | {has_b:4s} | {complexity}")
            
            # 验证条件
            if combined_size < expected_min:
                print(f"    ✗ 帧{i}: 合并帧太小，可能缺少数据")
                verification_passed = False
            elif ratio < 0.5:
                print(f"    ⚠ 帧{i}: 合并帧大小偏小，可能压缩过度")
            elif ratio > 0.8:
                print(f"    ✓ 帧{i}: 大小合理，可能包含B帧数据")
        
        print("-" * 80)
        
        if verification_passed:
            print("✓ 帧级别验证通过：合并视频的帧大小符合预期")
        else:
            print("✗ 帧级别验证失败：合并视频的帧可能缺少B视频数据")
        
        return verification_passed
        
    except Exception as e:
        print(f"逐帧验证失败: {e}")
        return False
    
    finally:
        # 清理临时目录
        if os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
            except:
                pass

def verify_video_structure(video_path):
    """验证视频结构"""
    ffprobe_cmd = check_ffmpeg()[1]
    if not ffprobe_cmd:
        return False
    
    try:
        print(f"\n分析视频结构: {os.path.basename(video_path)}")
        
        # 获取详细信息
        cmd = [ffprobe_cmd, '-v', 'error', '-show_format', '-show_streams', video_path]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            output = result.stdout
            
            # 检查是否有B视频标识
            if 'BVID' in output:
                print("✓ 发现B视频标识符")
            else:
                print("✗ 未发现B视频标识符")
            
            # 检查文件大小
            if 'size=' in output:
                size_line = [line for line in output.split('\n') if 'size=' in line]
                if size_line:
                    print(f"文件信息: {size_line[0]}")
            
            return True
        else:
            print(f"视频结构分析失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"视频结构验证失败: {e}")
        return False

def main():
    """主函数"""
    print("帧级别验证工具")
    print("=" * 50)
    
    # 检查测试文件
    original_a = r"C:\Users\<USER>\Desktop\下载的\aa.mp4"
    original_b = r"C:\Users\<USER>\Desktop\下载的\666.mp4"
    combined_video = r"C:\Users\<USER>\Desktop\下载的\aaveres.mp4"
    
    if not all(os.path.exists(f) for f in [original_a, original_b, combined_video]):
        print("错误: 找不到测试文件")
        return
    
    print(f"原始A视频: {os.path.basename(original_a)}")
    print(f"原始B视频: {os.path.basename(original_b)}")
    print(f"合并视频: {os.path.basename(combined_video)}")
    
    # 验证视频结构
    verify_video_structure(combined_video)
    
    # 逐帧对比分析
    success = compare_videos_frame_by_frame(original_a, original_b, combined_video)
    
    print("\n" + "=" * 50)
    if success:
        print("✓ 验证结果: 合并视频可能包含B视频数据")
    else:
        print("✗ 验证结果: 合并视频可能缺少B视频数据")

if __name__ == "__main__":
    import time
    main()
