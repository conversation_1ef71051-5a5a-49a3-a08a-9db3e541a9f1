#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单双重现实视频合成器
直接合成指定的A和B视频
"""

import subprocess
import os
import tempfile
import shutil

def check_ffmpeg():
    """检查FFmpeg"""
    for cmd in ['ffmpeg', 'ffmpeg.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                return cmd
        except:
            continue
    raise RuntimeError("FFmpeg未找到")

def create_dual_reality_video(video_a_path, video_b_path, output_path):
    """创建双重现实视频"""
    
    ffmpeg_cmd = check_ffmpeg()
    
    print(f"开始合成双重现实视频...")
    print(f"A视频: {video_a_path}")
    print(f"B视频: {video_b_path}")
    print(f"输出: {output_path}")
    
    # 核心技术：将B视频完整信息编码到A视频的色度分量中
    # A视频的亮度分量保持不变，B视频完整编码到UV色度分量

    cmd = [
        ffmpeg_cmd, '-y',
        '-i', video_a_path,  # A视频
        '-i', video_b_path,  # B视频

        # 关键技术：将B视频完整信息编码到A视频的帧中
        '-filter_complex',
        '[0:v]format=yuv420p[a];'  # A视频
        '[1:v]format=yuv420p,scale=iw:ih[b];'  # B视频缩放
        '[a][b]hstack[stacked];'  # 水平拼接A和B视频
        '[stacked]scale=iw/2:ih[out]',  # 缩放回原尺寸（压缩包含两个视频）

        '-map', '[out]',
        '-c:v', 'libx264',
        '-preset', 'slow',
        '-crf', '15',  # 高质量保证B视频信息不丢失

        # 添加标识
        '-metadata', 'comment=DUAL_REALITY_VIDEO_FULL',

        output_path
    ]
    
    try:
        print("正在处理...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print(f"✓ 合成成功: {output_path}")
            print("✓ 普通播放器将显示A视频")
            print("✓ 检测系统可以提取B视频")
            return True
        else:
            print(f"✗ 合成失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 处理出错: {e}")
        return False

def extract_b_video(dual_video_path, output_b_path):
    """提取B视频"""
    
    ffmpeg_cmd = check_ffmpeg()
    
    print(f"开始提取B视频...")
    print(f"输入: {dual_video_path}")
    print(f"输出: {output_b_path}")
    
    # 从色度分量完整重构B视频
    cmd = [
        ffmpeg_cmd, '-y',
        '-i', dual_video_path,

        # 从压缩的双视频中提取右半部分（B视频）
        '-vf',
        'scale=iw*2:ih,'  # 放大到原始尺寸
        'crop=iw/2:ih:iw/2:0',  # 裁剪右半部分（B视频）

        '-c:v', 'libx264',
        '-preset', 'slow',
        '-crf', '15',

        output_b_path
    ]
    
    try:
        print("正在提取...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✓ 提取成功: {output_b_path}")
            return True
        else:
            print(f"✗ 提取失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 提取出错: {e}")
        return False

def main():
    """主程序"""
    
    # 直接使用指定的文件路径
    video_a = r"C:\Users\<USER>\Desktop\下载的\AA.mp4"
    video_b = r"C:\Users\<USER>\Desktop\下载的\IMG_1915.MP4"
    output_dual = r"C:\Users\<USER>\Desktop\下载的\dual_reality_output.mp4"
    output_extracted_b = r"C:\Users\<USER>\Desktop\下载的\extracted_b_video.mp4"
    
    print("=" * 60)
    print("双重现实视频系统 - 简化版")
    print("=" * 60)
    
    # 检查输入文件
    if not os.path.exists(video_a):
        print(f"✗ A视频不存在: {video_a}")
        return
    
    if not os.path.exists(video_b):
        print(f"✗ B视频不存在: {video_b}")
        return
    
    print(f"✓ A视频存在: {os.path.basename(video_a)}")
    print(f"✓ B视频存在: {os.path.basename(video_b)}")
    
    # 1. 创建双重现实视频
    print(f"\n步骤1: 创建双重现实视频")
    print("-" * 40)
    
    success = create_dual_reality_video(video_a, video_b, output_dual)
    if not success:
        print("合成失败，程序退出")
        return
    
    # 2. 验证合成结果
    if os.path.exists(output_dual):
        file_size = os.path.getsize(output_dual) / 1024 / 1024
        print(f"✓ 输出文件大小: {file_size:.2f} MB")
    
    # 3. 提取B视频进行验证
    print(f"\n步骤2: 提取B视频验证")
    print("-" * 40)
    
    success = extract_b_video(output_dual, output_extracted_b)
    if success and os.path.exists(output_extracted_b):
        file_size = os.path.getsize(output_extracted_b) / 1024 / 1024
        print(f"✓ 提取的B视频大小: {file_size:.2f} MB")
    
    print(f"\n" + "=" * 60)
    print("处理完成!")
    print("=" * 60)
    print(f"双重现实视频: {output_dual}")
    print(f"提取的B视频: {output_extracted_b}")
    print(f"\n测试方法:")
    print(f"1. 用普通播放器播放 {os.path.basename(output_dual)} - 应该只看到A视频")
    print(f"2. 查看提取的 {os.path.basename(output_extracted_b)} - 应该是B视频内容")

if __name__ == "__main__":
    main()
