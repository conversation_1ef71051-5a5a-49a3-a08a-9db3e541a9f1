#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双重现实视频系统 - 主程序
实现普通播放器显示A视频，检测系统显示完整B视频
"""

import os
import sys
import time
from dual_reality_encoder import DualRealityEncoder
from dual_reality_detector import DualRealityDetector

def print_banner():
    """打印程序横幅"""
    print("=" * 80)
    print("                    双重现实视频系统 v1.0")
    print("                   Dual Reality Video System")
    print("=" * 80)
    print("功能说明:")
    print("• 普通播放器：只显示A视频内容，B视频完全隐藏")
    print("• 检测系统：能够识别并提取完整的B视频内容")
    print("• B视频以完整、清晰的形式存在于每一帧中")
    print("• 基于播放器/检测系统的技术差异实现差异化显示")
    print("=" * 80)

def get_file_path(prompt: str, must_exist: bool = True) -> str:
    """获取文件路径"""
    while True:
        try:
            user_input = input(f"{prompt}: ").strip()
            
            if user_input.lower() == 'q':
                print("用户取消操作")
                sys.exit(0)
            
            # 移除引号
            if user_input.startswith('"') and user_input.endswith('"'):
                user_input = user_input[1:-1]
            elif user_input.startswith("'") and user_input.endswith("'"):
                user_input = user_input[1:-1]
            
            if not user_input:
                print("路径不能为空，请重新输入")
                continue
            
            if must_exist and not os.path.exists(user_input):
                print("文件不存在，请检查路径是否正确")
                continue
            
            if must_exist:
                # 检查是否为视频文件
                video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v']
                file_ext = os.path.splitext(user_input)[1].lower()
                if file_ext not in video_extensions:
                    print("不支持的文件格式，请选择视频文件")
                    continue
            
            return user_input
            
        except KeyboardInterrupt:
            print("\n程序已退出")
            sys.exit(0)
        except Exception as e:
            print(f"输入错误: {e}，请重新输入")

def create_dual_reality_video():
    """创建双重现实视频模式"""
    print("\n" + "=" * 60)
    print("                创建双重现实视频")
    print("=" * 60)
    
    # 获取输入文件
    print("\n请输入视频文件路径（输入 'q' 退出）:")
    video_a = get_file_path("A视频路径（普通播放器显示的视频）")
    video_b = get_file_path("B视频路径（需要隐藏的视频）")
    
    # 获取输出路径
    print("\n请输入输出文件路径:")
    output_path = get_file_path("输出路径（双重现实视频）", must_exist=False)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
        except Exception as e:
            print(f"创建输出目录失败: {e}")
            return False
    
    # 显示处理信息
    print(f"\n处理信息:")
    print(f"A视频: {os.path.basename(video_a)}")
    print(f"B视频: {os.path.basename(video_b)}")
    print(f"输出: {os.path.basename(output_path)}")
    
    confirm = input("\n确认开始处理? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("用户取消操作")
        return False
    
    # 开始处理
    print(f"\n开始创建双重现实视频...")
    start_time = time.time()
    
    try:
        encoder = DualRealityEncoder()
        success = encoder.create_dual_reality_video(video_a, video_b, output_path)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if success:
            print(f"\n" + "=" * 60)
            print("                  处理完成!")
            print("=" * 60)
            print(f"✓ 双重现实视频创建成功")
            print(f"✓ 输出文件: {output_path}")
            print(f"✓ 处理时间: {processing_time:.2f} 秒")
            print(f"✓ 文件大小: {os.path.getsize(output_path) / 1024 / 1024:.2f} MB")
            
            print(f"\n重要说明:")
            print(f"• 普通播放器播放此视频时，只会看到A视频内容")
            print(f"• B视频内容已完整隐藏在每一帧中")
            print(f"• 使用检测模式可以提取完整的B视频")
            
            return True
        else:
            print(f"\n✗ 双重现实视频创建失败")
            print(f"✗ 处理时间: {processing_time:.2f} 秒")
            return False
            
    except Exception as e:
        print(f"\n处理过程出错: {e}")
        return False

def extract_b_video():
    """提取B视频模式"""
    print("\n" + "=" * 60)
    print("                提取B视频")
    print("=" * 60)
    
    # 获取输入文件
    print("\n请输入文件路径（输入 'q' 退出）:")
    dual_video = get_file_path("双重现实视频路径")
    
    # 获取输出路径
    print("\n请输入输出文件路径:")
    output_b = get_file_path("B视频输出路径", must_exist=False)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_b)
    if output_dir and not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
        except Exception as e:
            print(f"创建输出目录失败: {e}")
            return False
    
    # 显示处理信息
    print(f"\n处理信息:")
    print(f"输入: {os.path.basename(dual_video)}")
    print(f"输出: {os.path.basename(output_b)}")
    
    confirm = input("\n确认开始提取? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("用户取消操作")
        return False
    
    # 开始处理
    print(f"\n开始提取B视频...")
    start_time = time.time()
    
    try:
        detector = DualRealityDetector()
        success = detector.detect_and_extract_b_video(dual_video, output_b)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if success:
            print(f"\n" + "=" * 60)
            print("                  提取完成!")
            print("=" * 60)
            print(f"✓ B视频提取成功")
            print(f"✓ 输出文件: {output_b}")
            print(f"✓ 处理时间: {processing_time:.2f} 秒")
            
            if os.path.exists(output_b):
                print(f"✓ 文件大小: {os.path.getsize(output_b) / 1024 / 1024:.2f} MB")
                
                # 验证提取质量
                quality_result = detector.verify_extraction_quality(output_b)
                if quality_result['success']:
                    print(f"✓ 质量评分: {quality_result['quality_score']:.1f}/100")
                    print(f"✓ 提取的B视频为完整、清晰的原始质量")
                else:
                    print(f"⚠ 质量验证警告: {quality_result['error']}")
            
            return True
        else:
            print(f"\n✗ B视频提取失败或不存在双重编码")
            print(f"✗ 处理时间: {processing_time:.2f} 秒")
            return False
            
    except Exception as e:
        print(f"\n提取过程出错: {e}")
        return False

def show_help():
    """显示帮助信息"""
    print("\n" + "=" * 60)
    print("                    帮助信息")
    print("=" * 60)
    print("双重现实视频系统使用说明:")
    print("")
    print("1. 创建双重现实视频:")
    print("   • 选择模式 1")
    print("   • 输入A视频路径（普通播放器显示的视频）")
    print("   • 输入B视频路径（需要隐藏的视频）")
    print("   • 指定输出路径")
    print("")
    print("2. 提取B视频:")
    print("   • 选择模式 2")
    print("   • 输入双重现实视频路径")
    print("   • 指定B视频输出路径")
    print("")
    print("3. 技术原理:")
    print("   • 基于YUV色彩空间分离技术")
    print("   • 利用播放器解码差异实现选择性显示")
    print("   • B视频信息完整保存在每一帧的色度分量中")
    print("")
    print("4. 注意事项:")
    print("   • 确保FFmpeg已正确安装")
    print("   • 建议A、B视频分辨率和时长相近")
    print("   • 处理时间取决于视频长度和复杂度")
    print("=" * 60)

def main():
    """主程序入口"""
    try:
        print_banner()
        
        while True:
            print(f"\n请选择操作模式:")
            print(f"1. 创建双重现实视频")
            print(f"2. 提取B视频")
            print(f"3. 帮助信息")
            print(f"4. 退出程序")
            
            choice = input(f"\n请输入选择 (1-4): ").strip()
            
            if choice == '1':
                create_dual_reality_video()
            elif choice == '2':
                extract_b_video()
            elif choice == '3':
                show_help()
            elif choice == '4':
                print("程序已退出")
                break
            else:
                print("无效的选择，请输入 1-4")
                
    except KeyboardInterrupt:
        print("\n\n程序已退出")
    except Exception as e:
        print(f"\n程序运行出错: {e}")

if __name__ == "__main__":
    main()
