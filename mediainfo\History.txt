﻿Notes :
-------
+ Added
- Deleted
x Bug correction
#number is the identifier of bug report (B), requested feature (F) or patch (P)
bug reports and feature request are here :
https://sourceforge.net/p/mediainfo/_list/tickets

Known bugs
----------
- Languages (other than english and French) : not all words are translated, I need translators!
- Others? https://sourceforge.net/p/mediainfo/_list/tickets

Version 23.10, 2023-10-04
-------------
+ Italian language update
+ Languages: add 'fil' (Filipino)
+ Support of MPEG-H in MPEG-TS
+ MOV/MP4: caption probing time expanded from ~15s to ~30s
+ MPEG-7 and DVD-Video: provide title duration based on frame rate
+ WAV: better display of buggy WAV files have 2 fmt/data chunks
x MOV/MP4: fix lack of detection of CEA-608/708 if junk at end of stream
x DVD-Video: fix duration if more than 1 menu

Version 23.09, 2023-09-14
-------------
+ DTS-UHD support (contribution from Xperi)
+ MPEG-7 output update, supporting collections for DVD Video
+ ISO 9660: more metadata
+ AVC: read out of time code
x DVD Video: better support of ISO having several episodes
x MPEG Video: fix duration not including last field duration (interlaced content only)
x I754, AVC&HEVC: fix risk of crash with some streams

Version 23.07, 2023-07-12
-------------
+ USAC conformance checker: update DRC presence check
+ USAC conformance checker: sbgp presence check
+ USAC conformance checker: difference between extra zero bytes and other extra bytes
+ ISO 9660: support of DVD video, with option for listing all contents
+ MPEG-7: support of collections (beta)
+ More Blackmagic RAW meta kinds
+ DTS-HD: DTSHDHDR header support (used for raw DTS-HD files)
x ADIF: fix wrong detection of lot of files as ADIF (Android and MediaInfoOnline)
x USAC conformance checker: fix arith context handling in some corner cases
x ADM: some tweaks about FFoA/Start/End time codes
x Remove curl default ca info message in stdout

Version 23.06, 2023-06-28
-------------
+ USAC/xHE-AAC conformance checker
+ S-ADM: support of SMPTE ST 2127-1 / SMPTE ST 2109 / SMPTE ST 2127-10 (S-ADM in MGA in MXF)
+ S-ADM: add S-ADM version and support of 1/1.001 frame rates
+ ADM: show FFoA/Start/End as timestamp and timecode
+ MPEG-7 output update with more extensions
+ MPEG-TS: support of JPEG XS
+ DTS-UHD: support of DTS-UHD (a.k.a. DTS-X P2) in MP4
+ MP4: detection of VVC
+ MP4: support of media characteristicd (spoken dialog, translation, easy to read...)
+ MP4: support of more Blackmagic RAW Codec IDs
+ MP4: support of ipcm CodecID
+ MP4: support of service kind
+ HEVC: support of SMPTE ST 2094-10
+ HDR: display of all formats if more than 1 format is detected
+ Matroska: support of SMPTE ST 12 in block additions
+ HEVC: time code SEI readout
+ AVC & HEVC: active format description readout
+ MPEG-TS: support of SMPTE ST 2038 (ancillary data)
x ADM/Dolby: fix wrong FFoA with 1.001 frame rates
+ MOV/MP4: more info with tracks having unknown type
x MOV/MP4: avoid to parse too much content with non stripped timecodes
x MOV/MP4: avoid incoherent behavior if 2 tracks have the same ID
x TTML: fix default frame rate
x TimeCode: 1/1.001 frame rate was not always detected
x MediaTrace: fix some random blank outputs
x URL: remove query part of the URL in the FileExtension field
x Referenced files: fix handling of URL encoded with UTF-8 content
x Matroska: fix crash in support of HDR10+

Version 23.04, 2023-04-26
-------------
+ MXF: support of SMPTE ST 381-4 (AAC in MXF)
+ DTS: show MA or HRA tip in commercial name for DTS:X
+ DTS: detection of DTS:X not lossless
+ APT-X100 a.k.a. Cinema DTS: initial support
+ Matroska: support of HDR10+
+ MP4: more information about thumbnails
+ ID3v2: more information about thumbnails
+ VP9: initial support, for more information about chroma subsampling
+ AWS S3: support for reference files with AccessID:SecretKey@URL
+ Windows: fix some download errors with AWS S3 objects (libcurl update)
x AWS S3: fix errors with some special chars in SecretKey
x AWS S3: fix random credential issues with non geolocated URLs
x DTS: fix freeze with some DTS-HD not DTS:X files
x MPEG-TS: fix crash in HEVC_timing_and_HRD
x AAC: fix samples per frame with SBR streams
x FLAC: fix missing Tbc Tbr in ChannelLayout

Version 23.03, 2023-03-29
-------------
+ DTS: Detection of IMAX Enhanced
+ MOV/MP4: Add HDR Vivid format support
+ HEVC: Add HDR Vivid format support
+ MXF/PCM: detect silent tracks (full parsing only)
+ Monkey's Audio: support of 32-bit files, show version
+ MP4 audioProfileLevelIndication: add Low Delay AAC v2 Profile
+ MP4/MOV: support of FLAC
+ MOV/MP4: support of TTML with images
+ MPEG-7: 3 modes (strict, relaxed, extended)
+ MPEG-7: more sub-termIDs (AudioPresentationCS)
+ MPEG-7: Add more PublicIdentifiers
+ MPEG-7: more sub-termIDs (MP4, WAV, AVC, ProRes)
+ AVI/WAV: display of the kind of fmt chunk
+ AVC: detection of more profiles
+ ChannelLayout: difference between M (Mono) and C (Center, part of multichannel content)
+ AC-3: detection of channel layout also for encrypted content
+ AC-4 and MPEG-H 3D Audio: Merged channel layout (all sub-streams together)
+ DTS: Detection of real bit depth e.g. 20 instead of only byte aligned bit depth (16 or 24)
+ FLAC: support of BWF in Vorbis comments
+ N19/STL: codepage, subtitle count, max line per subtitle, more metadata
+ ISAN: detection of descriptions referencing an ISAN
+ AAC: detection of eSBR (and fix of random wrong PS detection)
+ Extract of time codes, XML format, currently only for for MXF
x MP4/MOV: fix freezes with some unknown udta atoms
x FLV: fix duration of 0 with some buggy files
x AVC: fix PTS of last frame
x FFV1: fix potential crash with malformed files
x AV1: add HDR format line and fix HDR values
x AAC and WAV: fix of channel layout display for 5 front channels
x AC-4: Tl/Tr mapped to to Tsl/Tsr
x FLAC: fix sampling count
x ID3v2: fix Genre not showing ID 0 (Blues)
x MPEG-7: VBR fix
x JSON/XML: Remove minus sign from element names
x Normalization of date/time in report

Version 22.12, 2022-12-22
-------------
+ WebVTT: more information (duration, start/end timestamp, count of lines...)
+ MP4/MOV: support of FLAC
+ MP4/MOV: support of LanguageIETF
+ ProRes: parse FFmpeg glbl atom for getting color range
+ AVI/WAV: detection of character set
+ WAV: display MD5 of raw content
+ FLAC: display MD5 of unencoded content
+ USAC: trace of UsacFrame() up to after preroll
+ MOV/MP4: option for parsing only the header, no parsing of any frame
+ MXF: option for parsing only the header, no parsing of any frame
x MXF: quicker parsing when fast parsing is requested
x I662, WAV: fix false-positive detection of DTS in PCM
x I1637, MPEG-Audio: proper support of Helix MP3 encoder detection and encoder settings
x I661, MXF: fix UKDPP FpaPass value sometimes not outputted
x S1182, Teletext subtitle: prioritize subtitle metadata other overs
x Matroska: Better handling in case of buggy AVC stream
x 22.2 audio: Fix name of 1 channel (Tll --> Tsl)
x AAC: fix wrong parsing of some bitstreams
x Fix crash with stdin input and ctrl-c
x Fix memory leak in JSON output

Version 22.09, 2022-10-04
-------------
+ Italian language update
+ USAC: IOD and sampling rate coherency checking
+ ADM: support of nested objects and complementary objects
+ AC-4: Display of Custom downmix targets
+ IAB: Parsing of IAB bitstream and ADM-like output
+ Frame rate: store FrameRate_Num/Den also for integer values
+ MPEG-4/MOV: support of time codes >30 fps
+ MOV/MPEG-4: List of QuickTime time code discontinuities
+ Dolby Vision: add info about more profiles
x Text streams: show stream frame rate if not same as container frame rate
x CDP: fix rounding of frame rate
x SCC: fix of CEA-608 FirstDisplay_Delay_Frames
x SCC: fix TimeCode_Last
x MPEG-4/MOV: last time code value for all kind of QuickTime time codes
x MOV/MPEG-4: Fix frame count for NDF non-integer frame rates
x JSON: fix invalid output in some corner cases
x Several other parsing bug/crash fixes (thanks to fuzzing by users)

Version 22.06, 2022-06-23
-------------
+ MXF: FFV1 support
+ Dolby Vision: add info about more profiles
+ AAC: check of missing ID_END and incoherent count of channels
+ NSV: better handling of buggy StarDiva agenda negative timestamps
+ Text: Show text frame rate
+ Text: frame rate precise numerator/denominator also for text streams
+ CDP: readout of display aspect ratio
+ MPEG-4/MOV: support of time codes >30 fps
+ TTML: Support of more timeExpression flavors
x ADM: correctly map Dolby binaural render mode to track UID
x Dolby Audio Metadata: first frame of action in HH:MM:SS:FF format
x Dolby Vision: profiles and levels in decimal rather than in hexadecimal
x MXF: fix of Dolby Vision Metadata not displayed if HDR10 metadata is present
x MPEG-4/MOV: avoid buggy frame rates by taking frame rate from stts atom
x CDP: better catching of wrong line21_field value
x NSV: better handling of invalid frames
x MXF: Include frame count in SDTI and SystemScheme1 time codes to time stamp conversion
x TTML: do not show frame rate if it is from MediaInfo options
x DV: timecode trace in HH:MM:SS:FF format

Version 22.03, 2022-03-31
-------------
+ NSV (Nullsoft Video): full featured support
+ NSV: support of proprietary StarDiva metadata (by reverse engineering)
+ HEVC: CEA-608/708 support
+ Dolby Audio Metadata: First frame of action, binaural render modes
+ Dolby Audio Metadata: 5.1 and 5.1.x downmix, 5.1 to 2.0 downmix, associated video frame rate, trim modes
+ MOV/MP4, TTML, SCC, MXF TC: time code of last frame
+ EIA-608: first displayed caption type
+ EIA-608: Maximum count of lines per event and total count of lines
+ EIA-608: duration of the visible content
+ TTML: Total count of lines
+ TTML: Maximum count of lines per event (including overlapping times)
+ TTML: Frame count, display aspect ratio
+ TTML: Support of timestamps in frames
+ SCC: Delay
+ Matroska: Encoding settings metadata support
+ MOV/MP4: Gamma metadata output
+ MPEG-4/MOV: difference between audio Center and Mono when possible
+ MP4/MOV: Support of dec3 atom in wave atom
+ MPEG-4/MOV: show both values in case of chan atom ChannelLayoutTag / ChannelDescriptions mismatch
+ MP4/MOV: Support of dec3 atom in wave atom
+ MXF: better support of AVC streams without SPS/PPS
+ ADM: display channel index of trackUIDs
x WAV: fix freeze with 32-bit PCM
x DPX: fix regression with DPX files more than 64 MB
x Dolby E: fix crash with some invalid streams
x E-AC-3: service kind was not correctly handled
x EXR: fix of bad handling of files with long names in attributes
x TTML: correct handling of 29.97 DF time codes
x AV1: fix of the parsing of some streams, especially the ones with HDR metadata
x WebVTT: was not correctly handling WebVTT header with comment
x Matroska: fix false positive detection of bad CRC32
x Several other parsing bug/crash fixes

Version 21.09, 2021-09-17
-------------
+ Graph view for 3D audio streams (thanks to graphviz)
+ ADM: full featured support (programmes, content, objects, pack formats...)
+ ADM: in WAV (axml, bxml), MXF
+ S-ADM in AES3: support of Levels A1 and AX1
+ MOV/MP4: support of Dolby Vision Metadata XML
+ MXF: detection of IAB
+ SMPTE ST 337 (AES3): support of subframe mode
+ HEVC: CEA-608/708 caption support
+ MP4/QuickTime: Android slow motion real frame rate
+ JSON output: add creatingLibrary field
x MPEG-4: read too much data with some predecessor definitions
x EBUCore: fix of fields order and types

Version 21.03, 2021-03-26
-------------
+ WAV: ADM profile detection of Dolby Atmos Master or MPEG-H
+ SMPTE ST 337: support of AC-4
+ AC-3/AC-4: show top layer channels after Lw/Rw, as it becomes the defacto standard layout
+ Dolby Surround EX and Pro Logic IIz detection
+ Matroska: add DV support
+ CLI: read from stdin
+ DV: remove check of zeroed bytes in timecode, considered again as valid timecode
+ TIFF; add support of compression codes 7 and 8
+ WAV: show bext (BWF) version in verbose mode / XML / JSON
+ MXF: detection fo DCI P3 mastering display color primaries
+ Options: add software version to text output
+ Options: add report creation timestamp to text output
+ macOS: native build for Apple Silicon (arm64)
x HDR: mastering max. luminance precision was wrong
x WM: fix EncodingTime parsing
x MOV/MP4: skip XMP huge atoms, fix
x MPEG-TS: fix inverted supplementary_audio_descriptor mix_type values
x AAC: fix File_Aac::is_intensity according to ISO/IEC 14496-3:2009
x I1353, MP4: Skip user data Xtra and free atoms
x FFV1: fix crash with some bitstreams parsing
x TIFF: fix division by 0
x RF64: fix the WAV malformed chunk size test
x Supported platforms: this is the last version compatible with Windows XP, macOS 10.5-10.9, RHEL/CentOS 6

Version 20.09, 2020-10-09
-------------
+ Dolby ED2: full featured support (presentations, presentation targets, beds, objects)
+ MKV: support of Dolby Vision metadata
+ MXF: detection of Dolby E hidden in PCM tracks having more than 2 channels
+ WAV: detection of Dolby E hidden in PCM tracks having more than 2 channels
+ CineForm: display of color space (including Bayer), bit depth
x WAV: more precise sample count
x SMPTE ST 337: catch of streams starting later than usual (probing increased from 4 to 16 PCM "frames")
x PNG: detection of additional alpha plane in color space
x MXF: detection of additional alpha plane in color space
x AVI: detection of additional alpha plane in color space
x MPEG Audio: was wrongly flagging Xing info tag as CBR
x VorbisTag: does not skip DISCID
x Miscellaneous bug/crash fixes

Version 20.08, 2020-08-11
-------------
+ MPEG-H 3D Audio full featured support (group presets, switch groups, groups, signal groups)
+ MP4/MOV: support of more metadata locations
+ JSON and XML outputs: authorize "complete" output
+ MPEG-4: support of TrueHD
+ WM: show legacy value of performer if not same as modern one
+ WAV: trace of adtl (Associated Data List) chunk
x URL encoding detection fix for URL having a query part (issue with e.g. pre-signed AWS S3 URLs)
x Don't try to seek to the end (false positive range related error with HTTP)
x DPX: don't load the whole file in RAM
x Opus: fix wrong channel mapping
x Miscellaneous other bug fixes

Version 20.03, 2020-04-03
-------------
+ AC-4 full featured support (presentations, groups, substreams)
+ MPEG-H 3D Audio basic support
+ MPEG-TS: audio preselection descriptor support
+ Dolby Vision v2 detection
+ MPEG-4: support of colr/nclx (color information) box
x URL encoding option fixes, permitting to use URL encoded or non URL encoded links
x AAC: fix SBR frequency when in ADIF
x DPX: ColorimetricSpecification and TransferCharacteristic were inverted
x Several crash and memory leaks fixes

Version 19.09, 2019-09-10
-------------
+ AC-4: basic detection, raw, in MP4 or TS
+ AC-3/E-AC-3: display time code of the first frame
+ Don't show anymore by default "encoded" bit rates and stream sizes
+ MOV: Decode more language codes
x MXF: some metadata were missing
x AC-3: AC-3 actually has no bit depth, removing the default 16 value
x AC-3/E-AC-3: fix bitrate info (so duration) with streams having a time code
x AC-3: parse more frames also when in MP4, in order to better detect JOC (Atmos)
x MP4: do not show audio bit depth if it is the "default" 16 (value is not trustable enough)
x ProRes RAW: we know only width and height
x SubRip: bad handling of files having a quote character

Version 19.07, 2019-07-16
--------------
+ Dolby E: readout of Dolby E program description
+ MXF: Detection of Dolby Vision
+ MP4: support of Spatial Audio Metadata
+ DV: color space is explicit
+ DV: audio format settings
+ Matroska: PCM bit rate
+ MOV, MXF: Time code frame rate
+ DV: DVCAM commercial name for locked audio and PAL 4:2:0
+ MXF: Time code track name
x USAC: frame rate was missing in case of non standard sampling rate
x USAC: fix infinite loop with some LATM streams
x WAV: MP3 delay should be added to BWF time reference
x TTML: fix wrong output with standalone files
x N19/STL: fix crash with some uncommon framerates
x VC-3: fix sub sampling with some v2 files
x DV: Time code frame number was wrong (divided by 2) for 50/60 fps content

Version 19.04, 2019-04-23
--------------
+ USAC: DRC effect types, Sample peak level, True peak level, Program loudness
+ HDR: SMPTE ST 2094 App 4 (including HDR10+) support
+ HDR: move HDR10, Dolby Vision and SL-HDR meta to specific generic "HDR Format" lines
+ Matroska: SMPTE ST 2086 (HDR10) support
+ Matroska: FieldOrder support
+ HEIF image format support
+ AV1: support of AV1 in MP4, HEIF, IVF
+ MOV: Add a lot more countries to AppleStoreCountry field internal list
+ MXF: Fix memory leak when fully parsing big file with acquisition metadata
+ HEVC: more HEVC profiles (Multiview, Scalable, Screen Content...)
+ AAC: better handling of corrupted streams
+ AAC: better handling of unknown channel layouts
+ AVC in MP4: better support of corrupted streams
x B1101, AVI: fix crash with some invalid streams
x B1101, SMPTE ST 337: fix crash with some invalid streams
x Matroska: chapters timestamp were not display if chapters have no name
x MXF: Fix false positive truncated file detection when there is no Random Index Pack
x AAC: channel layout typos (Rls instead of Lrs, Lr instead of Rb)
x ProRes: correctly show color space if alpha plane is present
x MPEG Audio: some VBR files use "Info" Xing header, so we ignore the  difference between "Info" and "Xing"
x I943, MPEG-4: wrong display aspect ratio in some corner cases (32-bit release only)
x I1096, OGG: assign METADATA_BLOCK_PICTURE tag to cover

Version 18.12, 2018-12-10
--------------
+ DCP: support of multi-reel packages
+ EBUCore: added some FFV1 related metadata
+ JPEG: better info display of CYMK files
+ Provide source of the color related metadata (container or stream) (hidden by default)
+ MXF: display more information when wrapper/essence values are detected as not same
+ MXF: ProRes profiles
+ MPEG-4: ProRes RAW support
+ MPEG-TS: add support of parsing some ETSI TS 103-433 messages
x MPEG-2 Video: variable GOP detection fix
x MPEG-7 export: some fields were missing due to the removal of some legacy fields
x ADTS: Fix display of channel count for 8-channel streams
x ID3v2: fix some date related issues
x I298, ID3v2: fix wrong read of recording date in some cases
x I1032, PBCore2: fix essenceFrameSize with non Video tracks
x I1096, JPEG: fix crash with one file
x Several other crash and memory leak fixes

Version 18.08.1, 2018-09-10
--------------
x Fix XML/MPEG-7/PBCore2 output discarding non ANSI characters

Version 18.08, 2018-08-31
--------------
+ Dolby Atmos (in E-AC-3 or TrueHD): support of bed channel count/configuration + objects count + complexity index
+ AC-3/DTS/AAC: display of info about legacy decoders behavior removed
+ AC-3/DTS/AAC: some changes in how format is displayed
+ AC-3/DTS/AAC: better split between technical names and commercial names
+ AAC: support of profile information from MP4_IOD_Tag AudioProfileLevelIndication
+ USAC (xHE-AAC) support
+ Audio channel layout: using a new terminology, better suited for 3D Audio, see https://mediaarea.net/AudioChannelLayout
+ DSD (DSF & DSDIFF) support
+ DXD (Digital eXtreme Definition) commercial name
+ Dolby Vision: use new form for profile (numbers instead of acronyms)
+ New format "Directory" when image sequence + audio file is detected (1 directory style for the moment)
+ PBCore2 export update, thanks to WGBH
+ MPEG-7 export update
+ NISO export update
+ AV1: support of AOmedia AV1 based on 1.0.0 specifications
+ ATRAC9 detection
+ Versionned RPMs
+ HEVC: better support of buggy SEI
+ ADTS: CodecID
+ Support of injection of external metadata
+ HTTPS: support of AWS extension "x-amz-*" in HTTPS headers, permitting to manage temporary credentials (AssumeRole)
+ MPEG-4, #1005: Obey edit list in QuickTime Timecode track
x MIXML: hide fields which were hidden in normal output
x Hybrid AC-3/E-AC-3 (in Blu-rays): bit rate info was wrong
x Lot of bug fixes, see full log for more info

Version 18.05, 2018-05-09
--------------
+ PBCore 2.1 export update, sponsored by WGBH as part of the NEH-funded PBCore Development and Preservation Project
+ TIFF: more IFDs are supported (density, software...)
+ NISO Z39.87 output
x Mastering Display Color Primaries: was always showing BT.709 instead of real value, when present
x Attachments: do not provide anymore attachments content in XML by default, fixes

Version 18.03.1, 2018-03-26
 --------------
x Fix regression on Windows with directory names beginning by n or r.

Version 18.03, 2018-03-19
--------------
+ AV1: support of AOmedia AV1 based on latest specifications draft, raw (OBU) and in MKV
+ MXF: HDR metadata support
+ MXF: detection and parsing of ProRes (SMPTE RDD 44)
+ MXF: framerate container/stream incoherence detection
+ DPX: endianess, packing mode, compression mode
+ AVC: add consumer camera recording date/time
+ AVC: add consumer camera model name and iris F number
+ JPEG: ICC parsing, display of ICC color space
+ EBUCore: possibility to inject external metadata in the output from MediaInfo
+ JSON output
+ Attachments: do not provide anymore attachments content in XML by default, reducing XML output size
x colour description: trying (again!) to have more coherent labeling
x DCP/IMF: fix crash with some CPL files
x I782, FFV1: Golomb Rice parsing was wrong
x I210, FFV1: remove sar_den test must be 0 if sar_num is 0
x AAC: SBR parsing issue with 3+ channel streams, with sometimes  false-positive PS detection
x BMP: was wrongly considering 4-bit without palette as with palette so  wrong bit depth
x DPX: some elements in trace were wrongly displayed (wrong endianess)
x B1082, Ancillary data: fix infinite loop

Version 17.12, 2017-12-21
--------------
+ MediaInfoOnline: https://mediaarea.net/MediaInfoOnline
+ JavaScript build and example
+ Dolby Vision: detection of Dolby Vision and display of profile for MPEG-TS and MP4 files
+ MPEG-4: Support of external time code tracks (.qtc)
+ JPEG 2000: Support of IMF profiles
+ F523, BDMV: Support of UHD Blu-ray playlist
+ Endianness and Sign report for PCM Matroska tracks
+ MPEG-4: Resolume DXV display
+ MPEG-4: support of file names >64 chars long or non ASCII for referenced files
+ Slight binary size optimizations
+ colour_description: some changes in order to have more readable names (DCI P3, Display P3, sRGB)
x MP4: crash with some HEVC streams with Dolby Vision
x VC-3: frame rate should not be detected as wrong when there are several frames per block
x Matroska: wrong color range info
x Matroska: fix crash with some corrupted files
x MXF: better support of height when there is an incoherence between header and footer
x transfer_characteristics: fix BT.2020 difference between values 14 and 15
x Trace: fix freeze with some files
x Trace: invalid character encoding with some MOV files
x Some memory leak fixes

Version 17.10, 2017-11-02
--------------
+ We need your support! Visit https://mediaarea.net/SupportUs
+ Version scheme is now YY.MM (year dot month, 2 digits each)
+ New MediaInfo XML output, with XSD, more suitable for automatic parsing. Use " --Output=OLDXML" for keeping previous behavior
+ New " --Info_OutputFormats" option for listing supported output formats
+ Universal Ad ID: refactored display, better display of value and registry, XML name slightly modified
+ MOV: support of HDR metadata (MasteringDisplayColorVolume, MaxCLL, MaxFALL)
+ BWF: display of UMID and loudness info
+ AAC: show program_config_element in trace
+ MPEG Audio: frame rate info
+ PCM in WAV and Matroska: Support of ValidBitsPerSample
+ I197, EBUCore: 1.8 output uses now final version of XSD and final XSD location
+ Matroska: tweaking frame rate empirical detection for some corner cases
x I1070, LAME 3.100 info tag was incorrectly parsed
x B1068, MPEG Audio: Incoherent duration between General and Audio parts, Audio part duration fixed
x Matroska: showing "A_MS/ACM" Matroska CodecID
x MXF: Fix crash with some buggy files
x MXF: was not well supporting MXF referencing only 1 file
x PCM in WAV: 8-bit content is unsigned and without endianess
x PCM in WAV and Matroska: More coherency between Wave info and ExtensibleWave Info (bitdepth, sign)
x WAV: GUID display was with first 8 bytes in wrong order
x Several crash fixes

Version 0.7.99, 2017-09-11
--------------
+ EBUCore: JSON output (--Output=EBUCore_1.8_JSON)
+ EBUCore: add writingLibraryName and writingLibraryVersion attributes
+ Ad-ID identifier display on a single line ("Value (Registry)" format)
+ MPEG-4: Better display of format of VobSub tracks
+ MPEG-4: CodecID is stsd name + ObjectTypeId for mp4a, mp4v, mp4s
+ AVC: preferred_transfer_characteristics support
+ MPEG Video, MPEG-4 Visual, AVC, HEVC, MPEG-4, Matroska: correct detection of RGB
+ matrix_coefficients: detection of Y'D'zD'x and Chromaticity-derived
+ AAC: info about SBR/PS being implicit/explicit/NBC (Not Backward Compatible)
+ AAC: indicate audioObjectType in CodecID
x Fix a weird 1.334 DAR due to a rounding issue

Version 0.7.98, 2017-08-08
--------------
+ Matroska: handling of files with Tracks element after Cluster elements
+ Matroska: detection of Duration tag alone and use it even if tag writing date is not there.
+ Matroska: mapping of colour description elements, timecode and handler name to corresponding MediaInfo fields
x I169, WAV: too much aggressive detection of wrong 5-channel audio, now limited on AC-3 and DTS
x transfer_characteristics and matrix_coefficients slight change in order to have them unique per ISO integer value
x EBUCore: All XML elements are correctly escaped
x PBCore: All XML elements are correctly escaped

Version 0.7.97, 2017-06-30
--------------
+ Ubuntu 17.04 packages
+ HEVC: support of stream having VPS hrd_parameters
+ FLV: support of FLV files with an ID3v2 header
+ FLV: detect some incoherent frame rates in buggy files
+ TIFF: support of more tags
+ I518, AAC: consider 4 back channels as 2 side + 2 back channels
+ Matroska: integrate all elements from Matroska specs in MediaTrace
+ WAV: parsing of MPEG Audio extension "mext" chunk and displayed in MediaTrace
+ MPC: channels count
+ AAC: ADTS/ADIF duration in case of full parsing and/or "risky bitrate estimation" option
x MXF: less false-positive detection of some files as MXF
x B1053, WAV: metadata coherency, ignore "fact" chunk more often in order to avoid bad information when this chunk is buggy
x B1029, DPX: DPX endianess not considered with some fields in MediaTrace
x Custom template: can check "Other" part as any other parts
x Matroska: fix parsing issue with small byte blocks are taken from the file e.g. when reading from HTTP link
x Matroska: files with unknown block size were flagged as truncated
x MediaTrace: values with a \n were breaking the text report lines
x Some typos (RefFrames, SPF)

Version 0.7.96, 2017-06-01
--------------
+ MPEG-4: display of recorded date from DV substream
x I505, AC-3: crash with some potentially malformed frames
x I477, AVC: fix hang when open .mpls from some 3D BD
x MPEG-4: does not show "1904" year is raw value is 0 (Mac/Linux)
x Dedicated read thread disabled by default (rare dead locks)
x #B1027, MPEG-TS: some files where detected as MPEG-TS or BDAV but they aren't
x Sequence of files : frame count was wrong with sequence of MPEG-TS files (was count of files)
x MXF: detection of more 608/708 with parsing of few frames in the middle of the file was broken

Version 0.7.95, 2017-05-04
--------------
+ EBUCore: EBUCore 1.8 with acquisition metadata output.
+ Better support of growing files, especially when accessed by FTP
+ Matroska: better support of some broken files (high EBMLMaxSizeLength, padding before start of EBML)
+ EXR: showing Multipart and Deep flags
+ EXR: show image compression method
x EBUCore: fixed display aspect ratio (was using rational)
x EBUCore: fixed frame rate factor (was num/den instead of factor)
x AVC: some streams with dynamic frame type were having a wrong frame rate
x MPEG-4: some old AAC tracks were not correctly detected
x Matroska: was sometimes displaying "Bit depth: Bit0" when bit depth is unknown
x Nut: fix crash with some files
x FFV1: PixelAspectRatio was an integer, switched to 3-digit rational
x DTS: fix frame count
x Dolby E: fix frame count
x EBUCore: fix regression creating invalid XML files
x AVC: some streams with dynamic frame type were having a wrong frame rate
x I490, EXR: was not supporting valid 31-char attribute names

Version 0.7.94, 2017-03-31
--------------
+ VC-3: detection of embedded time code
+ VC-3: better support of stream with width 960 or 1440
+ VC-3: support of version 3 (a.k.a. DNxHR), including profile and level for version 1/2 (a.k.a. DNxHD)
+ Matroska trace feature: reduction of its size
+ MXF trace feature: reduction of its size
+ Visual Studio 2017 project files
x M143, MXF with Acquisition Metadata: crash fixed
x Several crash fixes

Version 0.7.93, 2017-02-28
--------------
+ Matroska: detection of native FFV1 CodecID ("V_FFV1")
+ AC-3/E-AC-3: detection of Atmos inside core streams
+ AC-3/E-AC-3: slight reorganization of metadata display for dual mono and surround
+ AC-3/E-AC-3: "complete main" and similar info moved to "Service kind" dedicated line
+ AC-3/E-AC-3: more precision about how is built a stream (e.g. "E-AC-3+Atmos / E-AC-3" or "TrueHD+Atmos / TrueHD / AC-3")
+ WTV: basic detection
+ MPEG-TS: Detection of Opus
+ URL: "URLEncode" option for saying if the input should be URL encoded or not (default is guess = no change except if it contains spaces)
x MediaTrace: for bitstreams (not bytestreams), bit offset was wrong
x HLS: duration was sometimes wrong, reading only the first TS file duration. Now full duration is displayed
x MPEG-TS: if stream is encrypted or invalid, level was sometimes not the expected one for AVC (e.g. "BaseLine@3.0" instead of "Baseline@3")
x Matroska: FFV1 stream width/height was not initialized when Matroska track header width/height is after CodecID
x FFV1: fix potential crash with some buggy slice headers
x Matroska: crash in case of big attachment and CRC32 present

Version ********, 2017-02-02
--------------
x Fix a performance regression in the matroska parser

Version 0.7.92, 2017-01-31
--------------
+ #F507, MXF: detection of HLG Transfer Characteristic
+ #F508, HEVC: support of preferred_transfer_characteristics SEI (from HEVC/H.265 draft, preferred method for HLG in DVB)
+ MXF: parsing of AVC descriptors and crosscheck with the essence content
+ MP4: more AppleStoreCountry values mapped to countries, show the country number if unknown
+ File extension: test if the file extension correspond to the container format
+ AVI/WAV: test of truncated file
+ MIXML output: Format_Profile divided in Format_Profile, Format_Level, Format_Tier
+ ID3v2: TCAP tag is mapped to new field "PodcastCategory"
x MIXML output: some *_Original values were missing
x MXF/Teletext: was not correctly detecting non subtitle streams
x ID3v2: TP2 tag was incorrectly mapped to "Accompaniment", now mapped to "Album_Performer"
x ID3v2: TSO2 tag was incorrectly mapped to "Performer_Sort", now mapped to "Album_Performer_Sort"
x ID3v2: TCMP tag was displayed twice (2 "Compilation" lines)

Version 0.7.91, 2016-11-30
--------------
+ IMF and PTX: more languages detected from file names (but the full list of common languages tags still need to be added)
+ IMF and PTX: support of non-standard but common "LAS" = "Spanish (Latin America)" language code
+ MXF: Support of color primaries, transfer characteristic, coding equations defined in SMPTE ST 2067-21:2016 e.g. xvYCC or BT.2020
+ Minor performance optimizations and cleanup (Thanks to Pavel Pimenov)
+ MediaTrace optimization for Matroska
x Fixed parsing of FFV1 v0/v1
x PTX: fix crash due to bad parsing of some file names while looking for track language

Version 0.7.90, 2016-10-31
--------------
+ #M94, WAV: support of file with a buggy RIFF header
+ Matroska: detection of segment size of zero (compatibility risks e.g. Windows 10 Media Player does not play such file)
+ MXF: detection of some incoherences between header and footer
+ MXF: display of Locked information
+ N19/STL: support of 48/50/60 fps content
+ N19/STL: display of time code of first frame
+ AC-3: bit depth
+ MPEG Video: CBR bitrate mode information based on vbv_delay
+ DXW: support of fake time code attribute
x Teletext in MPEG-TS: CodecID, format (classic teletext or subtitle), video delay were sometimes not displayed
x PDF: fixed crash with some files with full analysis set
x #B485, BMP height is negative
x Several minor fixes

Version 0.7.89, 2016-09-30
--------------
+ QuickTime: support of Panasonic AVC-Intra 50/100/200 without SPS/PPS ("aixx" and "AVin" CodecID)
+ More QuickTime/MP4/AVC/HEVC color descriptions based on future ISO 23001-8:201x/PDAM1
+ FFV1: handling 16+ bitdepth (YUV and RGB) while handling buggy version <= 3 YUV 16-bit streams
+ Improved growing file detection, option for forcing the growing file test
+ Matroska: support of video FieldOrder, MatrixCoefficients, BitsPerChannel, Range, TransferCharacteristics, Primaries
+ Acquisition Metadata: support of more elements (IrisTNumber, IrisRingPosition, FocusRingPosition, ZoomRingPosition, ColorMatrix)
+ Add stream counts to MIXML output
+ I242, AVI/Matroska: mapping of mjp2 to JPEG 2000 format name
+ MPEG-4 Visual: parsing of Studio profiles, providing width/height/frame rate...
+ MXF: reading MPEG-4 Visual profile from MXF sub-descriptor and/or EssenceCompression
+ MXF: reading Intra GOP info from descriptors
+ Sequence of images: detection of sequence even if the provided path separator is the Unix one (Windows only)
x Acquisition Metadata: IrisFNumber, FocusPosition, LensZoom were not correctly reported
x LXF: fixed crash in case of some malformed files
x LXF: reject bad frames instead of displaying wrong duration and bit rate

Version 0.7.88, 2016-08-31
--------------
+ MediaInfo distributed with HTTP/HTTPS support: support of Amazon S3 with Signature Version 4
+ FFV1: parsing speed slight improvement
x Duration: fixed regression in last release, sometimes duration was displayed with only count of minutes

Version 0.7.87, 2016-06-30
--------------
+ Refactoring of the trace feature, for better performance and some bug fixes
- Visual C++ 2012 project files removed
x Review of symbols display, now using standard display (e.g. "k" instead of "K", " min" instead of "mn"...)
x XML output: revert to old versioning method (version is MediaInfo software version)
x I63, EBUCore/FIMS outputs: typo correction about WritingLibrary and WritingApplication
x Matroska: files with CodecPrivate element before CodecID element where not always correctly parsed
x OGG: crash/incoherent behavior with some buggy OGG streams having invalid chunk sizes

Version 0.7.86, 2016-05-31
--------------
+ FFV1 parsing optimization, avoiding by default a too verbose trace
+ Matroska: more elements support and typo fixes in the trace
+ #I172, Trace feature: provide the name of the main parser in the media element
+ Matroska: consider all values below 0x10 EBML names as 1-byte junk
x Matroska: better support (including speed improvement) of huge lossless frames (e.g. 20 MB FFV1 4K)
x #I144, Python binding: Python 2 on Linux does not automatically provide the locale to the shared object
x HTML output: don't escape carriage returns from the input file
x FFV1: some streams were rejected despite the fact they are valid
x Python binding: some errors during call of Get() API with Python3 on Linux/Mac

Version 0.7.85, 2016-04-29
--------------
+ FFV1: ScanType and ScanOrder
+ Detection of Omneon VBI and move of the VBI track from Video part to Others part
+ N19/STL: Support of etection of less standard frame rates (23.976 and 29.970)
+ Teletext in MPEG-TS: Teletext not subtitle moved to "Other" part
+ Teletext in MPEG-TS: display of CodecID and timestamp of first frame
x Teletext in MXF: some IDs were wrong (when there are more than one Teletext service)
x MPEG-4/MOV: default of raw audio to Signed in case of stsd atom version <2
x MPEG Video: some CEA-608/708 captions were not correctly detected due to some packets discarded by the parser with interlaced content
x MPEG-4/MOV: Dolby E with 2 or more audio services were not correctly reported

Version 0.7.84, 2016-03-31
--------------
x #I122, MPEG-4/MOV: Crash if mdhd timescale is 0
x MPEG-4/MOV: Infinite loop if malformed stsc / stsz (detected by fuzzing)
x MPEG-TS: some DVB Subtitles were not detected
x HLS: better handling of media playlists having EXT-X-BYTERANGE

Version 0.7.83, 2016-02-29
--------------
+ HEVC: Maximum Content Light Level (MaxCLL) and Maximum Frame-Average Light Level (MaxFALL), metadata mandated by CEA-861.3 for HDR support
+ HEVC: Mastering display color primaries and luminance (based on SMPTE ST 2084), metadata mandated by CEA-861.3 for HDR support
+ HEVC: SMPTE ST 2048 and SMPTE ST 428-1 transfer characteristics
+ HEVC: Chroma subsampling location (indication of the location type described in the HEVC spec)
+ MPEG-TS: ATSC Modulation Mode (Analog, SCTE_mode_1 aka 64-QAM, SCTE_mode_2 aka 256-QAM, 8-VSB, 16-VSB)
+ #B981, MP4: support of buggy file having "hint" SubType 
x HLS: better handling of media playlists having EXT-X-BYTERANGE

Version 0.7.82, 2016-01-27
--------------
+ Matroska: CRC-32 validation
+ Matroska: support of padding/junk at the start of a segment
+ Matroska: trace is activated for all elements (but report is still based on the first element met)
+ Matroska: add an intermediate level in the trace for the raw stream parser
x FLV: potential infinite loop fixed
x #B966, DTS: DTS-HD HR 3840 not detected anymore
x AC-3: wrong sample rate with 32 kHz streams
x #B948, EBUCore 1.6: invalid output due to position of containerEncoding element
x #B957, MPEG-7 output: No XML encoded value output

Version 0.7.81, 2015-12-31
--------------
+ Acquisition Metadata: support of all SMPTE RDD18 elements
+ Matroska: cover presence and content of the cover, thanks to Max Pozdeev
+ #F446, Matroska: Handling of cropping values, thanks to Max Pozdeev
+ Improvement of Python binding: Mac Os X support, Python2 and Python3 can use same MediaInfoDLL.py
+ #F484, AVI: OpenDML Interlaced / Progressive scan type detection
+ MP4: support of AtomicParsley imdb tag
x #B959, MPEG-TS: MPEG-1 Video appeared as MPEG-2 Video
x #B914, Matroska: Undefined number of chapters in some M4V with Timed Text, thanks to Max Pozdeev
x #B962, Matroska: negative timecodes were not correctly handled
x #B964, FLV: was hanging trying to open some FLV files
x JPEG in AVI or MOV: better handling of buggy APP0/AVI1, avoiding some false positives about interlacement
x DVCPRO HD: some containers consider DVCPRO HD as with width 1920 despite the fact it is 1280 or 1440, using 1280 or 1440 in all cases

Version 0.7.80, 2015-11-30
--------------
+ Matroska: support of MKVMerge statistics tags (duration frame count, stream size, bit rate) per track, thanks to ndjamena
+ FLAC: Channel positions, thanks to ndjamena
+ FLAC: difference between detected bit depth and stored bit depth
+ MPEG-TS: if DTVCC transport stream is present and no DTVCC service descriptor, scan also in the middle of the file in order to detect more caption services
+ Subtitle frame rate computing if frame count and duration are available (hidden by default)
+ Subtitles in Matroska: count of elements
+ Matroska, MXF and MP4/MOV: detection of truncated files
+ DTS: difference between ES Matrix and ES Discrete
+ DTS: display ES Matrix or ES Discrete even if HRA or MA is present
+ DTS: difference between DTS-HRA with 96k option and pure DTS-96/24
+ DTS: detection of DTS:X
+ Samples per frame info
+ AC-3: detection of Atmos inside TrueHD
+ Video frame rate: showing precision of 1/1.001 frame rates (e.g. "23.976 (24000/1001) fps" and "23.976 (23976/1000) fps")
+ MPEG-4/MOV: showing the complete list of compatible brands in the CodecID field
+ MPEG-4/MOV: Alternate groups
+ MPEG-4/MOV: "Disabled" tag
+ MPEG-4/MOV: "Forced" tag
+ MPEG-4/MOV: showing links between tracks (chapters for, subtitles for, fallback for)
+ MXF: handling of more acquisition metadata items
+ MXF: Package name
+ AVC: Store method of interlaced content (Interleaved Fields or Separated Fields)
+ EBUCore: acquisition metadata (Proof of concept, for feedback only)
x Matroska: frame rate detection algorithm revisited, less wrong numbers are expected
x SDP/Teletext: some pages were sometimes (when present in 2 different SDP lines) displayed several times
x MPEG-4/MOV: some hint tracks were not displayed

Version 0.7.79, 2015-11-02
--------------
+ CLI/DLL only, XML: new option --Output=MIXML, with XML v2.0beta1 status, not for production, see https://github.com/MediaArea/MediaAreaXml for more details
+ MediaTrace: support of more than 1 file in XML output.
+ CLI/DLL only, XML: new option --Output=MAXML, with XML v0.1 status, in order to have bot MediaInfo and MediaTrace in the same output, not for production, see https://github.com/MediaArea/MediaAreaXml for more details
x MediaTrace: fixed some invalid outputs
x #B951, Amazon S3 support (REST API v2), CLI/DLL only and if compiled with libcurl support: URL without credential were badly interpreted

Version 0.7.78, 2015-10-02
--------------
+ MOV: AVrp CodecID support
+ Video Stored_Width/Stored_Height and Sampled_Width/Sampled_Height added (hidden by default)
+ Speed optimization for the parsing of tiny files e.g. JPEG files
+ Option (command line / DLL) --Legacy=0 for disabling some legacy fields
+ Option (command line / DLL) --Output=MAXML, XML with MediaInfo and MediaTrace output together, technology preview (not for production)
x MPEG-TS: Teletext and Teletext Subtitle were missing in the menu list
x Chroma subsampling "4:4:4" was sometimes indicated for RGB, which is not useful

Version 0.7.77, 2015-09-02
--------------
+ #B941, MXF: files having only a video stream and an ancillary data stream were having incorrect second video stream
+ MOV: detection of r210 CodecID as raw RGB
+ Ancillary data: detection of all metadata blocks (previously: only the first one was detected)
x MPEG-TS: Wrong demux of TSP (188+16 TS) files having PES with only padding
x MediaTrace #2: XML malformed with Flags items (hotfix, flags meaning disabled in XML output)
x MediaTrace #3: XML malformed with some MP4 files
x MediaTrace #6: XML duplicated attributes
x MediaTrace #10: versioned xsd, creating library name and version
x MediaTrace: XML content was not escaped
x #B947, Amazon S3 support (REST API v2), CLI/DLL only and if compiled with libcurl support: Analyze file on s3 was not working if secret key contains / character

Version 0.7.76, 2015-08-06
--------------
+ XML output: line breaks and indents in between attributes for readability
+ Trace feature: XML trace update, only if compiled with trace feature
+ Amazon S3 support (REST API v2), CLI/DLL only and if compiled with libcurl support
+ FFV1: improved slice analysis (currently activated only with trace feature and for 1st frame)
x MXF: optimization of the parsing, reading a lot less data (e.g. can be reduced from 1 GB to 10 MB with some UHD files)
x MXF: wrong frame count with files not having the video stream as the first stream
x Dolby E in MPEG-TS: "SMPTE ST 302" information was accidentally removed
x MPEG-TS: avoid filling delay from file name, not applicable on MPEG-TS
x MXF: better handling of huge padding blocks, better handling of descriptors without link to a TrackID
x IMX: streams claiming that they are IMX are actually pure MPEG Video, probing both IMX and MPEG Video

Version 0.7.75, 2015-06-30
--------------
+ MXF: consideraing 60 fps timecode tracks with 2 components having a difference of 2 frames as a single timecode
+ EBUCore 1.6: switch to the link of the final XSD
x XDCAM: some directory structures were wrongly detected as XDCAM structure having a XML file
x MXF: SDTI 60 fps times were wrong
x #B927, DPX: date/time specific DPX format was used instead of the ISO-like one
x #B927, EBUCore: invalid content in attribute startDate
x ProRes: streams with apcs CodecID were displayed with an incoherent bit depth instead of no bit depth

Version 0.7.74, 2015-05-25
--------------
+ FIMS: Preliminary version (not for production)
+ D-10 audio: display of real bitrate beside the encoded bitrate
+ VC-3: detection of CDP packets (608/708 captions), created by Nexio, inside the VC-3 private data
+ AES3: generic term AES3 replaced by more precise SMPTE ST numbers (302, 331, 337)
+ NUT: basic detection
+ FFV1: more parsing of the bitstream, support of any version of FFV1 in MOV and Matroska
+ DPX: color space and frame rate
x #B906, Matroska: duration was missing if Info block is at the end of the file
x #B908, AC-3: bit depth removed
x #P86, MPEG-4: moov_trak_edts_elst Atom was ignoring version of the atom
x Dolby E: the MXF channel count value was sometimes reported instead of the real channel count
x VorbisComment: WAVEFORMATEXTENSIBLE_CHANNEL_MASK "0X" ("x" in uppercase) is accepted now
x EBUCore: TotalProgrammeDuration was wrongly filled with IdentClockStart value
x EBUCore: Source/Material moved from timecodeStart to timecodeTrack
x MPEG-4: info is missing for some streams in some specific cases

Version 0.7.73, 2015-04-09
--------------
+ BPG: basic support, thanks to Kurtnoise
+ CAF: basic support of Apple Core Audio Format, sponsored by FlavorSys
+ JPEG-2000: Display of profile (Rsiz)
+ JPEG-2000: detection of XYZ colorspace (based on D-Cinema profile)
+ FFV1 in MOV: more details (version...)
+ MOV/MPEG-4: handling of clcn (little endian nclc) Color parameter type
+ #P84, Matroska: Add TEXTST support to the MKV Parser, thanks to Kurtnoise
+ #P85, MPEG-TS: Add TEXTST support to the MPEG-PSI Parser, thanks to Kurtnoise
+ MediaInfoDLL interface: clean up, more debug features added
+ MediaInfoDLL interface: Giant threads lock removed
+ #F460, VC-3/DNxHD: detection of RGB 444 and other SMPTE ST 2019 (2014) new CIDs
+ VC-3/DNxHD: version number (HVN)
+ Clean aperture size is move from width/height to its own field
+ HEVC: tier
+ MXF: writing library and writing application fields are cleaned up
+ ProRes: support of 4444 profiles
+ CAP: detection of CAP files from Cheetah and Lambda
x B886, XML and HTML outputs were broken
x B902: EBU AspectRatio invalid in case of non classic numerator:denominator format
x #B758, VC-3/DNxHD: wrong color space and subsampling in case of unknown CID, now empty if format version is not known
x #B903, MXG: Incorrect timecode track used for AS-11 DPP MXF files
x #B904, MXF: Handling repetition of Header Metadata in MXF files
x MXF: AFD value was wrong (displaying the complete byte, but AFD is only 4 bits of this byte)
x DTS: some streams in Little endian were not detected
x MPEG-4: some files were having an incorrect frame count
x AVC: Some SCTE 128 caption streams were displayed twice
x BMP; accepting files with file size information set to -1
x RF64: samplesCount was not always right
x MOV: avoid wrong parsing in case of "colr" atom with "prof" color parameter type
x DCP/IMF: ID has now the AM/PKL CPL order in order in all cases (not only when there is more than one CPL)
x #B893, MXF: Crash with Panasonic P2 3.0 files
x DPX: time information was with native ":" character
x Images sequence: "Delay" field is filled from the number in the name of the first file
x FLV: some files were not having the right duration
x DPX: Cineon files were detected as DPX Version 1, version number was not corresponding to the real version

Version 0.7.72, 2015-01-07
--------------
+ MXF: MXF version (e.g. "1.3")
+ Option "File_IsImageSequence" for being able to skip frames in a sequence of files.
+ EBUCore: EBUCore 1.6 draft output, sponsored by EBU
+ EBUCore: AS-11 to EBUCore mapping, sponsored by EBU
+ EBUCore: more technicalAttributes, sponsored by EBU
+ MXF Acquisition Metadata (RDD-18, EBU Tech 3349) basic support
x EBUCore: time code tracks moved to their own "format" block
x EBUCore: audioTrackConfiguration removed (not the expected behavior)
x EBUCore: OverallBitRate information changed from technicalAttributeString to technicalAttributeInteger
x PBCore: invalid output in case of time code stream, fix thanks to Dave Rice
x PBCore2: codecid changed, thanks to Dave Rice
x OP-47/SDP/Teletext: some streams were not detected
x Previous version was built with SSE2 instructions, switching back to SSE instructions
x AVC: infinite loop fix
x AVC: trying to avoid the wrong detection of AVC in the case of invalid (e.g. encrypted) streams
x Crash on Windows Vista and Win7 pre-SP1 and a CPU having FMA support, due to a bug in MSVC2013, since 0.7.72. Thanks to Pavel Pimenov for the report and patch
x MXF: AS-11 Typo (SerieTitle changed to SeriesTitle), TotalProgrammeDuration added
x ID3v2: support of 3-byte ID3v2.3 tags
x CEA-608 in MPEG-4: demux PTS was wrong in case of multiple blocks in one frame
x MOV: better Grey scale files and color palettes handling, with help from Vittorio Giovara

Version 0.7.72, 2014-11-09
--------------
+ AS-11 (Core, Segmentation, UK DPP items) display
+ MXF: support of TTML (SMPTE ST 2052) detection
+ MXF: option --File_Mxf_TimeCodeFromMaterialPackage for using the time code track from Material package instead of Source package (CLI/DLL only)
+ Duration in HH:MM:SS:FF format (hidden by default)
+ AVC: detection of Intra profiles
+ MXF: both Material and Source package time codes are displayed
+ MPEG-TS: more information for Teletext subtitle e.g. "For hearing impaired people"
+ Detecting sidecar XML files for captions (e.g. TTML/DFXP), option for CLI and Lib
+ AVC and HEVC: colour_range information (Limited/Full), thanks to Vittorio Giovara
+ OP-47/SDP/Teletext: detection of magazine numbers
+ MOV/MPEG-4: basic support of iTunMOVI tag
+ MOV/MPEG-4: support of track title and few other track related tags
+ MOV/MPEG-4: detection of Dolby E in a stereo track
+ AVC: using the first pic_struct as fallback for scan order if other methods fail, thanks to Smit for the idea
+ IMF: better compatibility with packages from different vendors
+ PBCore 2.0 technical metadata output option, thanks to Dave Rice
+ WMV: Time code track and value of first frame display, sponsored by AVCOM
+ MPEG Video: Open/Closed GOP status
+ HEVC: Support of Pixel Aspect Ratio in VUI, thanks to Kurtnoise
x SMPTE ST 331: wrong channel positions
x B872, MOV: StreamOrder field was wrong in case of chapter references
x More coherency between the File interface and the By buffer interface
x Matroska: wrong dection of video/audio delay in case of B-frames before the first I-frame
x Time code striped/not striped information is moved from "Format_Settings" to "TimeCode_Striped"
x SMPTE ST 337: infinite loop during scanning of some non ST 337 streams
x MP4/MOV: Using less data during detection of Dolby E hidden in a PCM track
x Matroska: some crashs after file name of attachments update
x MXF: MXF time code track was not displayed with some compilation options (e.g. the default Windows build)

Version 0.7.70, 2014-09-03
--------------
+ DTS Neural Audio: display of DTS Neural Audio descriptor information (matrix encoded channels count and position), sponsored by Numericable
+ FFV1: version, bit depth, color space, chroma subsampling, versions 0 to 3, sponsored by NOA Audio Solutions
+ HuffYUV: version, bit depth, color space, chroma subsampling, versions 1 to 3, sponsored by NOA Audio Solutions
+ PDF: basic detection (goal is to detect PDF/A profile)
+ HLS: support of encrypted streams (key must be in FileName.FileExt.key or provided via the library API)
+ CDP: using CDP service descriptor for the list of 708 services + language information
+ MXF: showing the real bit depth ("Quantization bits"); the count of bits stored in the file is in a new field "Stored bit depth" if it differs
+ MXF: Audio "frame rate" (how PCM content is split in packets) information
+ MXF: Audio block alignment byte size (hidden by default)
+ VC-3: adding the commercial name "DNxHD" + bitrate class
+ MXF: SMPTE ST 377-4 (MXF Multichannel Audio Labeling Framework)
+ MXF: "ChannelLayoutID" new field (hidden by default) containing the MXF Channel Assignment Label
+ Wave64: Duration
+ CDP: frame rate
+ IMF: improvements (still work in progress)
+ QuickTime: ia13 CodecID
+ CDP: using ccsvcinfo_section() for service presence and language information
+ MXF/CDP: law rating (from CEA-608 XDS) information added (other containers were already supported)
+ CEA-608: Program name information
+ BMP: support of OS/2 headers
+ HLS: com.apple.streaming.transportStreamTimestamp support
+ ISM: ISMT / text (TTML) streams support
+ MXF: detection of buggy BlockAlign from some muxers, so PCM bit depth is correctly detected despite the buggy metadata
+ HEVC: library name of some encoding libraries
+ MPEG-2 Video: picture structure for interlaced content (interlaced content can have a frame structure)
+ HLS: support of some encrypted files (AES-128 with default IV and per segment), key is provided by the command line --File_Encryption_Key=(Base64 data)
+ HEVC: adding support of x265 library name and x265 encoding settings
+ ProRes: more details about ProRes in AVI or Matroska
+ DV: support of AVd1 CodecID
+ CMake: scripts improvements, thanks to Ivan Romanov
+ Matroska: file name of attachments, thanks to Marshall
x HEVC: some streams with VUI were not parsed
x MPEG Video: was not correctly handling 1000:1001 frame rates and NDF time codes combination during frame count computing
x MPEG Video: was not correctly detecting the time stamp of the first frame in case of closed GOP with first frame not the I-frame
x XDCAM: information about the MXF header presence was not provided if MOV header has a glbl atom
x Some specific C++11 code is removed for full compatibility with older C++
x MXF: Time codes values were buggy in case of MXF with pre-charge ("Origin" not 0)
x MPEG-4 Visual: wrong analysis in case on video_object_layer_shape not set to regular
x MPEG-4/MOV: trying to display the summary differently when there is a difference between container and raw stream channels count information (long term method to be discussed)
x Non-Unicode version was not building anymore
x DyLib: trying to have a better support of pure C compilers

Version 0.7.69, 2014-04-24
--------------
+ MPEG-TS: support of HEVC streams with stream_type of 36
+ EBUCore 1.5: update with support of more fields and valid against final XSD
+ Added interlaced content store method (Separated fields or interleaved fields)
+ HEVC: Better support of the different containers (including MP4/MKV/FLV)
+ #B844, Matroska: detection of covers, thanks to Max Pozdeev
+ Sequences of files: optimization of the detection of sequences with 200k+ files
+ Sequences of files: File_IgnoreSequenceFilesCount option for speeding up the detection of sequences (con: no detection of the latest file in the sequence)
+ Sequences of files: File_IgnoreSequenceFileSize option for speeding up the detection of sequences (con: no detection of total size of the sequence)
+ Sequences of files: detection when there is additional characters after the numbers (e.g. "filename[000001].png")
+ MPEG-TS: detecting sequences of files
+ ADTS: detecting sequences of files
+ MediaInfoList: removing files detected as part of a sequence
+ Arri Raw: basic detection
+ DDS (DirectDraw Surface) support
+ OpenMG (.oma) partial (Atrac3 only) support
+ WebVTT detection
x #B841, QuickTime TFF/BFF: using now Technical Note TN2162 as the reference documentation for 'fiel' atom
x Matroska: slow parsing of some files
x MOV/IMX: IMX was not detected when glbl atom is present

Version 0.7.68, 2014-04-02
--------------
+ QuickTime: new field ScanOrder_Stored, displayed when display and Stored orders are not same (replacing ScanOrder_StoredDisplayedInverted field)
+ IMF: better support of IMF files having more than 1 ressource per track
+ IMF: better support of IMF files EntryPoint and SourceDuration fields
+ MPEG-TS: EBP detection
+ Excel VBA example (32 and 64 bit), in contrib directory
+ Node.js examples added, in contrib directory
+ GXF: support of DVCPRO HD
+ GXF: Media Type is reported
+ HEVC: added support of scaling_list_data
+ HEVC: deactivated general_profile_compatibility_flag validity test because some encoders do not implement it correctly
+ MPEG-4/MOV: Speed improvement, especially for full parsing of 100+ GB files.
+ File reading: Speed improvement of big files, especially for full parsing, with the help of a dedicated reading thread
+ Java binding: adding support of memory buffer as input, example with InputStream class (from RandomAccessFile or from FileUrl, including Amazon S3 authorization)
+ PTX: more files are supported
+ Ancillary data: more fomats are detected (OP-47 WST, OP-47 Multipacket, WSS, CEA-608, MPEG-2 Recoding Information...)
+ EBUCore output: update, with EBUCore 1.5 support
+ MXF: detection of Sony RAW SQ (by reverse engineering, not sure)
+ F432, AVI: detection of MPEG Video with Codec Id 0x02000010
+ AVI: detection of captions inside MPEG Video
+ MPEG-4/MOV: showing non-media streams (e.g. hint/rtp tracks)
+ #P81, HEVC: raw stream frame rate info, thanks to Kurtnoise
+ AIFF/Dolby E: detection duration of hidden Dolby E increased from 0.1 second to 1 second
x #B833, FLV: some (out of specs) files were no more fully detected
x #B828, HEVC/H265: parsing of final specification (vui_parameters() specs were modified)
x #B835, HEVC/H265: bug fixes, thanks to KP Lau
x #B838, AVC/H264: bug fixes, thanks to KP Lau
x #B831, MPEG Audio: files with MusiFile header/trailer are correctly detected (but header/trailer are currently discarded)
x #B836, XCode 5.1 compilation issue fixed, thanks to masterkain
x STL: better support of non-English characters during decode
x MXF: some properties were not displayed with OP-Atom files
x JPEG-2000: wrong implmentation of COD parsing, with undefined behavior for some files
x MXF: potential crash with small files
x AAF: potential crash with small files
x HLS: potential crash with small files
x MXF: some video streams were wrongly detected as MPEG-2 video
x MXF: better detection of some weird cases with interlace JPEG-2000 content
x MXF: better support of files with more than 16 MB per frame (2.5 Gbps bit rate)
x configure.ac: removal of problematical typos, thanks to Dmitrij D. Czarkoff
x Files with the wrong extension: the file was sometimes fully read (very slow!)
x AVI: Huge files (2GB+, with OpenDML) were sometimes parsed slowly
x MOV: better display of metadata when tvsh atom is present
x AC-3: some dependent streams ere not correctly detected

Version 0.7.67, 2014-01-10
--------------
x MXF: duplicate display of some time code streams in ancillary data
x B814, AAC: Wrong detection of audioObjectType==29

Version 0.7.66, 2014-01-09
--------------
+ EBUCore 1.4 output support
+ IMF AssetMap (AM), PackageList (PKL) and CompositionPlaylist (CPL): improved support
+ Channel layout information for AAC, AC-3, E-AC-3, DTS, Dolby E
+ MXF: CodecID field (EssenceContainer-EssenceCompression)
+ Pro Tools 10+ Session file (.ptx) support, by reverse engineering (=it may not work)
+ Playlist files: trying to detect language and channel layout from file names
+ QuickTime: new field ScanOrder_StoredDisplayedInverted, set to "Yes" when display and Stored orders are inverted
+ Wave: Detection of wrong byte rate in header in order to provide right duration of some PCM streams
+ ARIB captions: detection of captions in ancillary data (tested with MXF)
+ AAF: basic support of playlist files
x QuickTime: false-positive detection of incoherency between container and raw stream scan order due to inverted display and tored orders
x MXF: Dolby E stream not detected in some cases
x #P78, HEVC: general_level_idc shall be set equal to a value of 30 times the level number, not 10 times, thanks to Kurtnoise
x C# binding example: was not working with 2GB+ files, was parsing sometimes the whole file
x #B802, AAC: Infinite loop, thanks to Sébastien Michel
x #B805, AC-3: Segfault on files falsely recognized as AC3 files, thanks to Gildas Desnos
x #B808, PCM: Infinite loop, thanks to Gildas Desnos
x #B809, APE tags: Infinite loop, thanks to Gildas Desnos
x #B810, AVC: Infinite loop, thanks to Gildas Desnos
x #B813, CLI_Compile.sh bug ("eats all resources" while compiling)
x LATM: false-positive detection of some files
x MXF: Crash with some files

Version 0.7.65, 2013-11-20
--------------
+ MXF: forcing detection of MPEG Video in case EssenceCompression is not present but MPEG2VideoDescriptor is present
+ GXF: detection of some captions and time codes event if they are not present at the beginning of the file (testing middle of the file)
+ DASH MPD: basic support
+ HDS F4M (Flash Media Manifest): basic support
+ DCP AssetMap (AM), PackageList (PKL) and CompositionPlaylist (CPL): basic support
+ IMF AssetMap (AM), PackageList (PKL) and CompositionPlaylist (CPL): basic support
+ Mac dylib: looking for the dylib in @executable_path and CFBundleCopyExecutableURL dir
+ AAC: option for instantaneous bitrate in fast detect mode (MediaInfoLib only)
+ FTP (custom builds only): support of UTF-8 file names
+ Colour description: colour_description_present added, better separation between bitstream values and container values
+ MPEG-4: RLE, color space and bit depth
+ Law rating: support of CEA-608 XDS Content Advisory in MPEG-PS, MPEG-Ts, LXF, GXF
+ MPEG-4/MOV: Bug found in one file, sample size is 16 with a 32-bit CodecID ("fl32"), correcting the output of MediaInfo
x #B775, AVI: AVI can use negative height for raw to signal that it's coded top-down, not bottom-up
x #B780, MPEG-TS: crash with some files having PAT/PMT change between begin and end of the file
x #B782, PBCore 1.2: some fields were not in the right order
x #B784, some humain readable strings were not removed when the corresponding field is removed
x #B787, MPEG-4/QuickTime: Erratic appereance of Bitrate Mode
x #B798: setlocale() remove from DLL
x #B785, DVCPRO HD: streams can be 8 or 10 bit, removing hard coded value from DV parser (MXF header value is used instead when applicable)
x MPEG-4: wrong demux of some E-AC-3 streams
x AAC: detection of HE-AACv2 was missing if the library is configured with fast detection
x MPEG Video: wrong computing of duration of raw stream in case of drop frame time code
x Automation, StreamKind type was set to integer, it is text
x MPEG-4: was reading lot of useless bytes from disk when the raw stream format is not known
x AVI: crash with some malformed text streams
x Reference/playlist files were not supported from FTP (custom builds only)
x MPEG-4/MOV: ScanOrder was using "stored" value instead of "displayed" value
x MXF: Detection of Dolby E was not working in some cases (regression in 0.7.62)
x MPEG-4/MOV: freeze with some files having mono 32-bit PCM

Version 0.7.64, 2013-07-05
--------------
+ New canonical URL of the website: http://MediaArea.net/MediaInfo
+ E-AC-3: support of streams having substreams (e.g. more than 6 channels)
+ JPEG: detection of YUVA, RGB, RGBA and YCCK color spaces
+ MPEG Audio: detection of Id3v1 inside an MPEG Audio frame
+ Matroska: support of HEVC/H.265 (based on specifications draft from DivX inc)
+ Canopus: detection of scan mode, scan order, pixel aspect ratio
+ MD5 generation option (work in progress)
+ Id3v2: reading of all Id3v2 blocks (no more only the first one) at the beginning of the file
+ MPEG-4: support of few additional iTunes tags, thanks to Kurtnoise
+ AVI: detection of Ut Video, thanks to Kurtnoise
+ MXF: detection of Dolby E even if EssenceCompression is not SMPTE 337
+ AIFF: detection of Dolby E
+ AIFF: detection of not aligned Dolby E
+ ARIB B24/B37: Caption_conversion_type display (HD, SD, Mobile)
+ MPEG-TS: displaying CEA-708 service and CEA-608 presence if the corresponding ATSC descriptor is present
+ MPEG-TS: quicker analysis in the case of quick parsing option
+ #F412, Matroska: Handling of files having no DocType
x #B761, MPEG-TS/ARIB: crash with some streams, thanks to maki
x #B765, Matroska: was parsing the complete file if a stream indicated in the header is not present
x #B763, MXF: detection of incoherency of channel count between bitstream and wrapper
x #B762, Matroska: detecting 23.976 frame rate (instead of 23.962 fps due to imprecise timestamp)
x #B759, QuickTime: detection of time codes having tcmi not embedded in tmcd box
x #B766, RMVB: a/v delay is incorrect, currently disabling it
x #B768, MPEG-4, crash with some MPEG-4 files
x #B769, MPEG-4, crash with some MPEG-4 files
x #B764, AVC: wrong parsing of some streams having pic_scaling_matrix
x AVC, crash with some AVC streams
x AAC, infinite loop with some AAC streams
x FLV, infinite loop with some FLV files
x Matroska: crash with some malformed files
x MOV: crash/long parsing with some malformed files
x AC-3: crashes and freezes fix
x Java 64-bit: Count_Get(StreamKind) was always returning 0
x Python 64-bit: Count_Get(StreamKind) was always returning 0
x DTS: some streams were not detected
x DTS: some memory leaks with 14-bit streams
x SMPTE ST 302: memory leaks
x SMPTE ST 337: memory leaks
x Total failure if MEDIAINFO_REFERENCES_NO #define was used
x QuickTime: Time code name is in "Title" field
x MPEG-4/QuickTime: handling of weird "negative" frame duration is stts

Version 0.7.63, 2013-05-13
--------------
+ Switched to a BSD-2-Clause license
+ LXF: AFD (from ancillary stream) support
+ Detection of some side car files and showing them as a single package (optional)
+ MOV: more channel positions information
+ TTML: detection
+ SAMI: detection
+ ID3: updated list of genres, thanks to Mats
+ .so: search a local copy of the library before trying default path
+ AVI: Support of Adobe PARf (Aspect Ratio) chunk
+ VC-3: Scan order
+ #P65, Flac/Ogg DISCTOTAL metadata support, thanks to Kurtnoise
+ #P67, MOV, add Hap Video to the database, thanks to Kurtnoise
+ #P67, Matroska, add VP9 to the database, thanks to Kurtnoise
+ #P68, FLV, add HEVC detection in the FLV parser
+ #P66, MOV, tref/chap handling, with chapters information, thanks to Kurtnoise
+ #P72, MOV, HEVC/H265 detection and basic support, thanks to Kurtnoise
+ #F382/P75, PMP format detection
+ MPEG-TS, HEVC/H265 support
+ 16:10 DAR known value
x #B742, MPEG-4/MOV, crash with some truncated/invalid files
x #B746, MPEG-4/MOV: crash with files having moof atom and no moov atom
x #B747, Inconsistent hang with buffer API
x #B757, MediaInfoDLL.py MediaInfoList was not working, thanks to Elbenfreund
x #B740, XML: dt:dt is replaced by dt (for binary data)
x MXF: Dolby E streams starting only at the second or third frame were not well detected
x MPEG-4/MOV: reducing analysis time for I-frame only video streams
x CEA-708: weird behavior phantom streams are displayed) in case of buggy CEA-708
x AVI: crash with audio delay and AvgBytesPerSec of 0
x Wrong demuxing of the caption stream in the case of AVC streams without frame_mbs_only_flag but having 2 fields in one slice.
x DPX: parsing was very slow with a sequence of files.
x MXF: Pre-charge duration was not read, time code of the first frame was wrong if "Origin" is not 0
x FLV: file was sometimes (e.g. most of the file padded with zeroes) fully parsed
x VC-3: Using values from specifications (based on compression ID) instead of SBD/SST/SPL/APLF from bitstream

Version 0.7.62, 2012-02-22
--------------
+ ARIB STD B24/B37 caption detection (both Japanese and Brazilian versions)
+ LXF: support of AVC, VC-3, MPEG audio, AC-3, Dolby E, and AAC detection and analysis
+ AC-3: support of 22.05 kHz streams (out of specs but they exist)
+ MOV: AIC (Apple Intermediate Codec) scan type detection
+ MOV: support of AVID metadata (interlacement information)
+ Time code dedicated tracks (MOV, MXF, GXF)
+ Time code track (MPEG-4/MOV, GXF, MXF)
+ Time code in SDTI (MXF)
+ Time code in System scheme 1 (MXF)
+ Time code in SMPTE RP 188 (aka SMPTE ST 12-2 aka ATC aka VANC) (GXF, LXF, MXF)
+ Time code in SMPTE RP 196 (aka HANC)
+ MPEG Video Time code
+ MPEG-TS: format_identifier, pointer_field, section_length (hidden by default)
+ CEA-608/708: caption detection duration is increased to 15 seconds (or 64 MB) in order to miss less caption content
+ Image files as a video stream: file name of the last file
x #727, MOV: crash with some malformed files (Time scale set to 0)
x #728, AAC: crash with some malformed streams
x #681, AVI: was not analyzing VBR streams without bit rate info in header
x #736: Division by 0 with 0-byte files
x Id3v2: crash with some malformed tags
x Bit rate display was "0 bps" if the real bit rate is more than 4 Gbps
x Division by 0 in case of 0 byte long file
x MPEG-4: wrong muxing mode information in case of A/53 captions in MPEG Video in MOV
x P2 Clip: wrong uppercase/lowercase in the file name of source files.
x MOV: PCM endianness was sometimes wrong
x MPEG-4: JPEG interlacement was sometimes wrong
x MPEG Video: wrong DAR information in case of DAR change between begin and end of the file

Version 0.7.61, 2012-10-22
--------------
+ MPEG-TS: SCTE 35 and KLV streams are better displayed (in Menu part)
+ MPEG-TS: Menu part contains the list of PID with unknown format
+ MPEG-TS: Menu part lists PID in the PMT order instead of increasing order
+ Display of both container and stream scan type and scan order
+ DV100: scan order
+ MXF: scan order
+ MPEG-TS: Maximum and minimum overal bit rate (only if parse speed is set to 1)
+ MPEG-TS, MPEG-S, MXF, AVI, WM: StreamOrder field added
+ MXF: better support of malformed VANC streams
+ MPEG Video: improved detection of the GOP (more frames are used), "Variable" value
+ MPEG-PS: FirstPacketOrder info added
+ SkipBinaryData option
x #3564456, Matroska: some (other) streams were wrongly detected sa VFR
x #3570092, Id3v2: support of old COM and ULT fields
x CEA-708: crash with some malformed streams
x MPEG-TS: crash when PCR is corrupted (same value at different offsets)
x QuickTIme: wrong channel count report in case of buggy chan atom. Now the stream description has priority over chan atom
x E-AC-3: duration was wrong in some cases
x Matroska: random wrong analysis in case of SimpleBlock mode
x #3574436, MOV: hang on files having buggy "alis" atom
x MPEG-TS: bit rate mode detection was sometimes too much strict about CBR
x DV: wrong detection in case of buggy audio header (if present and set to 0xFF)
x MPEG-4: crash in case of buggy aperture size atom
x MediaInfo_Const.h was missing in the DLL package for Mac
x MPEG-PS: detection of phantom streams
x WAV: detection of malformed >4GB WAV files was no more working
x DTS: computing bit rate from frame size instead of targeted transmission rate
x DTS: setting the bit rate to "unknown" for Master Audio instead of instantaneous bit rate
x DTS: Display of endianess and word size was not coherent

Version 0.7.60, 2012-09-02
--------------
+ MPEG-TS/PS: improved detection of buggy time stamps
+ DPX: color primaries and transfer characteristics
+ MPEG-TS: Added support of scrambled streams without transport_scrambling_control bit set (e.g. PlayReady)
+ MPEG-TS: Name of some scrambling algorithms
+ MPEG-TS: detection of CBR/VBR at container level
+ MPEG Video: better detection of variable GOP
x Matroska: some streams were wrongly detected sa VFR
x #3538378, XML output: invalid characters, now if there is an invalid character, data is transported in base64
x LXF: wrong video bit rate with some files
x AC-3/E-AC-3: hang up with some Little Endian streams
x AAC: wrong min and max bit rate in case of partial (default) parsing, disabling it
x AVC: crash or hang up with some malformed/scrambled streams
x Opus: wrong duration in case of non-48kHz stream
x MOV: 25 fps + drop frame time codes were not handled correctly

Version 0.7.59, 2012-08-08
--------------
+ License: Switched back to LGPLv2+Exceptions
+ #3555183, PCX support, thanks to Lionel Duchateau
+ #3555182, PSD support, thanks to Lionel Duchateau
+ #3555181, Matroska: ALAC detection, thanks to Lionel Duchateau
+ #3540425, OGG/MKV: Opus speech format support, thanks to Lionel Duchateau
+ #3531808, AVI: detecting more inconsistencies in stream durations
+ GXF: crash with Time code tracks without frame rate info
+ MPEG-4: stream order (hidden by default), in order to provide the same numbers as mkvtoolnix 5.2+ Track ID
+ QuickTime: default channel map is "L R" for stereo streams (as it seems to be in QuickTime player)
+ MPEG-4: support of WMA (version 1, version 2, Pro, Lossless) in MPEG-4
+ FLV: handling of metadata with an underscore before the real metadata name
+ MXF: support of files with header missing TrackNumber in the descriptor (if it is present only in footer)
+ MXF: Language from DMS-1
+ ProRes: analysis of the ProRes raw stream (including scan order for interlaced content)
+ colour_primaries, transfer_characteristics, matrix_coefficients: canonicalization of results
+ MPEG-4 Visual: colour_primaries, transfer_characteristics, matrix_coefficients
+ ProRes: colour_primaries, transfer_characteristics, matrix_coefficients
+ GIF: Display Aspect Ratio
x #3533984, different behavior depending of compilation options (so Linux version was missing some info)
x MPEG-4: audio/video delay was wrong in case of negative delay
x CEA-608: Memory leaks removed
x AVC: crash in case of analyzing some invalid SEI
x MPEG Audio: crash with some files having Lyrics 2 tags
x MPEG Audio: crash with some files having APE tags
x AVI: secondary genre comes after primary genres in the "Genre" field
x FLV: better handling of files containing more than 1 meta chunk
x MPEG-TS/MPEG-PS: was aborting during full parsing in the case of very damaged streams
x Vorbis: infinite loop if codebook_entries>=256
x Id3v2: crash with some unsynchronized frames, especially with UTF-16 comments
x Id3v2: Wrong mime type of covers
x MPEG-PS: crash in case of language info in descriptors
x Java binding: crash with MediaInfo::Inform() (Windows 32-bit only)
x MPEG-TS: false-positive in case of some MPEG-4 files with wrong extension
x FLV: crash in some specific cases (malformed files)
x 3548314, MVC: Scan type was wrong with MVC-only (without the underlying AVC stream) stream
x 3553588, MPLS: stream duration was wrong with standalone (without the referenced M2TS) files
x 3553588, MPLS: incoherent behavior with MPLS having more than one PlayListItem
x 3554154, MPEG-TS: crash with some corrupted streams
x MOV: all EIA/CEA-608 captions were not well detected
x Matroska: Trying to better detect VFR streams, frame rate was wrong in case of interlaced content

Version 0.7.58, 2012-05-28
--------------
+ AC-3: Little Endian streams support
+ LXF: AVC streams support
+ ISM: better support
+ File referencing other files (HLS, ISM...): menu in case there is more than 1 stream per referenced file
+ MPEG-TS: option for keeping streams detected at the beginning then disabled in a an update of the PMT (activated by default)
+ MPEG-PS: program_map_section support for uncommon streams embedded in MPEG-PS
x Referenced files (MXF, HLS, MOV, P2, XDCAM...): issues with source name, track order, files size
x MPEG-TS/MPEG-PS: regression, some files with AC-3/DTS/DVD subtitles were not well analyzed anymore
x MPEG-4 channel mapping: Lt and Rt (matrix-encoded) channel mapping were missing
x GXF: handling of buggy files having non-PCM AES3 codec identifier but actually having PCM
x MPEG-4: better support of MPEG-4 files having corrupted metadata atom
x 3529510, EIA/CEA-708: was not detected if the stream was not present at the beginning, thanks to Pete Chapman

Version 0.7.57, 2012-05-02
--------------
+ #3513490, Vorbis comment (Flac, Ogg): more tags are supported
+ XML-based formats (P2, XDCAM, DCP, ISM, HLS...): support of UTF-16 encoding
+ MPEG-4: for buggy PCM, prioritizing the codec ID "in24" = 24-bit against the bit depth field
x #3516900, Vorbis comment (Flac, Ogg): trying to do better mapping of PERFORMER and ARTIST tags
x MXF: wrong video frame count in some cases
x #3517374, GCC 4.7: compilation issues removal, thanks to SpepS
x MPEG-PS: some files were not well demuxed so analysis was sometimes wrong (especially macroblock parsing)

Version 0.7.56, 2012-04-08
--------------
+ Better support of machines lacking of std::stringstream
+ Better support of machines requesting explicit stdc++ library link option (e.g. some ARM embedded devices)
x #3515515, MPEG-4: crash with MPEG-4 container + H264/AVC video stream
x #3515393, MPEG Audio: infinite loop (freeze) with some files
x #3514677, Video: Well known 1.85:1 display aspect ratio was incorrectly displayed as 16:9
x #3513908, File interface: No output if filename contain a colon
x #3515893, MPEG-4: some specific files were not detected
x AVI: infinite loop (freeze) with some files (having index containing 0-sized chunk)
x AVC: memory leaks
x libcurl support: libcurl config from MediaInfo is compatible with libcurl+gnutls
x #3515857, CLI only: --LogFile carriage return format was not the one of the OS

Version 0.7.55, 2012-04-05
--------------
+ AC-3: support of little endian streams
+ LXF: support of format version 0
+ HLS: support of .m3u8 index and sequence referencing a bunch of MPEG-TS files
+ MPEG-4: Added support of glbl atom and corresponding 4CC (ai12, ai15, ai1q, ai5q)
+ MPEG-4: Added detection of files with mx5p files wrongly filled with raw MPEG Video instead of MXF
+ MPEG-TS: Detection of 20-bit Dolby E even if the SMPTE 302 M header is set to 24-bit
x #3513490, Id3v2: mapping of "Encoded by" and "Encoding library" is conform to the specs now
x MXF: hang up with some clip-wrapped files
x MPEG-4: AVC-100 bit rate was not coherent depending of the container (MPEG-4 or MXF)
x reVTMD output is disabled due to its non-free (point of view of FSF and DFSG) licensing.

Version 0.7.54, 2012-03-13
--------------
+ #3480111, Matroska: parsing of WebM-style frame rate info
+ #3499859, ALAC: parsing of the alac atom, for real bit depth / sample rate
+ #3487601, DV: fields order (TFF/BFF)
+ MPEG-4: more video 4CCs binded to MPEG Video
+ H.263: raw stream parsing (width, height, aspect ratio), comparison with container data
+ Speed improvements
+ MPEG-PS: supporting parsing of some non-conform files
+ Full support of CEA-608 (separation of CC1-CC4, T1-T4)
+ #3494722, MPEG-4: trying to detect wrong duration in track header
+ MPEG-4 with embedded MXF track (XDCAM...): separation of video bitrate and padding bitrate
+ Compound streams (e.g. DV): separation of video bitrate and audio bitrate
+ Blu-ray: LPCM mono real bit rate is separated from the encoded (stereo) bit rate
+ Support of https, sftp scp protocols (custom builds only)
+ AVI: vprp (OpenDML) display aspect ratio support
x #3480111, Matroska: some frame rates are incorrect in the file, trying to detect this kind of malformed file
x #3479494, AVC: segmentation fault
x #3440638, AAC: wrong detection for some malformed AAC streams
x #3440638, MPEG-4: wrong analysis of some files having track header after media information
x #3480111, MXF: Height was wrong for MXF with FrameLayout = Mixed fields
x #3468235, Blu-ray: displaying PGS in all cases (even if PES is not detected, they are too much rare)
x #3498846, MPEG-4: delay between audio and video was no more detected
x #3495573, MPEG-4: crash with some files having fragments
x MPEG-4: channel position in case of channel atom is configured with ChannelBitmap
x MPEG-TS: crash with some buggy files (a PID indicated as PSI and PES at the same time)
x AES3: not detecting Dolby E stream if there is guard band before the Dolby E frame, in MPEG-TS
x DPX: some files with some invalid fields were not detected
x DTVCC Captions: crash with some buggy streams

Version 0.7.53, 2012-01-24
--------------
+ DV: option for ignoring transmitting flags (TF1/TF2/TF3) (DLL and CLI only)
+ Matroska: ProRes detection
+ MPEG-4: official DTS CodecIDs (dtsc/dtsh/dtsl/dtse) support, thanks to Lionel Duchateau
+ Matroska: stream order (hidden by default), in order to provide the same numbers as mkvtoolnix 5.2+ Track ID
+ #3471516, BLu-ray: wrong channel count for mono/3-channel/5-channel/7-channel PCM streams
+ AVI: ISMP (SMPTE Time code), Tdat tc_o/tc_a (Adobe Premier time code) support
+ reVTMD output
+ --Output is synonym of --Inform option
x QuickTime: crash and sometimes wrong info with some files having compressed header
x MPEG-4: commercial format typo error (EX422 instead of HD422)
x MXF: handling wrong MXF header having frame height instead of field height
x #3471053, Tags: Id3v1 tag was used instead of Id3v2 if the file is short
x #3463117, MPEG-TS: crash if Conditioal Access PID is same as the PES
x Custom output: better handling of cases with special character strings (\n...) in the file content
x #3440664: Audio only AVI file is missing duration
x #3453476: detection so incorrect duration information in tkhd atom
x Detailled XML output was producing duplicate xml-tags

Version 0.7.52, 2011-12-19
--------------
+ MXF with referenced files: if the referenced file is not available, trying to open local files
+ MPEG Video: GOP size for I-Frame only streams
+ MXF: support of CEA-608 in ancillary data for some other formats than MPEG Video, if there is no B-frame
+ LXF: support of SMPTE ST291/CDP/CEA-608/CEA-708 in ancillary data
+ WAV: better handling of files not having word alignment
x DV: crash (division by zero) in some cases
x DV: DVCPRO HD was sometimes not detected (low bitrate)
x MXF: Crash if AFD field has an invalid value
x MXF: Wrong endianess for some big endian PCM streams
x MXF: some MXF referencing files have wrong duration
x MXF: duration was wrong with some specific files
x DVD-Video: detection of 20-bit and 24-bit PCM
x XML output: it was sometimes containing some invalid characters
x MPEG-4: considering default char set as ISO-8859-1
x MXF: better handling of referencing files having the same ID for all tracks
x MXF: Handling of MXF files with wrong FooterPartition field
x MXF: Some captions (not starting at the beginning of the file) were not detected
x WAV: duration was missing is some cases
x RMP3 support was broken

Version 0.7.51, 2011-11-11
--------------
+ #2872918, MKV: add support for default and forced track flags
+ #3418881, RK Audio format support, thanks to Lionel Duchateau
+ #3418883, LA: version field, thanks to Lionel Duchateau
+ MPEG-4: basic support of Aigo .3dv files
+ MPEG-2 Video: color display info (colour_primaries, transfer_characteristics, matrix_coefficients)
+ QuickTime: color display info (colour_primaries, transfer_characteristics, matrix_coefficients)
+ QuickTime: ProRes LT, Proxy, 4:4:4 profiles detection
+ QuickTime: mpeg CodecID support
+ Template inputs are now insensitive to carriage return kind (Windows \r\n, Mac \r, Linux \n)
+ MPEG-TS: Support of ISO 8859-2 in EPG
+ MPEG-4: more CodedIDs supported
+ GXF: more info about DV streams
+ GXF: Handling of files with more than 1 Time code
x #3414326, GXF: using only the first map chunk for duration calculation
x #3414513, Id3v2: was not able to extract covers with UTF-16 description
x #3417908, video from several files: crash if there is not enough place for the frame number
x #3433602, DVD-Video (IFO): Crash when scanning some malformed IFO files
x Matroska: parser hanging in case of huge zero-padded files
x MPEG-4: was not providing some info about tracks with sample table before media header
x MXF: better support of corrupted indexes
x #3429831, MediaInfo library: unload wrong DLL, thanks to McSpecky
x MPEG-TS: Better handling of EPG running_status flag
x MXF: Handling of "file:///" in Network loacators
x FLV: audio delay was sometimes wrong
x H264: Buffer size was in bytes instead of bits like with other formats
x #3429196: no output if absolute file name, on Linux
x #3187050, FLV: wrong detection when FLV header is corrupt
x BLu-ray: crash if MPLS files are on the root of a disk
x Blu-ray: parsing was very slow if MPLS was pointing thousands of times on the same file
x #3292830, Matroska: crash if AAC sample rate is not provided by the container

Version 0.7.50, 2011-09-23
--------------
+ DVB Subtitle: region_width / region_height / region_depth  (hidden by default)
+ MPEG-4: hdv6 and mx4p CodecID support (MPEG Video)
+ AVI: v210 CodecID (YUV) detect
+ #3411596, CDDA support, thanks to Lionel Duchateau
+ MXF: support of AC-3
+ #3411999, AVi: detection of GeoVision MPEG-4, thanks to Lionel Duchateau
+ #3411999, MPEG-4: detection of VC-1 (WMV3), Nellymoser and WMA2, thanks to Lionel Duchateau
x AVI: Better support of 2 video streams (e.g. "3D AVI") in a file
x MPEG-TS: Duration was not provided in some cases
x FLV: Delay and duration for AAC was wrong if delay was not 0, thanks to Justin Greer http://zencoder.com
x #3408005, IFO: crash with some files
x MPEG-4: some bitrates where wrong if smooth streaming is used

Version 0.7.49, 2011-09-09
--------------
+ AES3: analyzing AAC, AC-3, MPEG Audio embedded in AES3 stream
+ GXF: detection of DolbyE in AES3 in GXF
+ WAV: detecting AES3 non-PCM audio inside WAV
+ MPEG Video: more precise duration for 29.97 fps material having time code without drop frame flag set
+ MPEG-4/MOV: delay from time code track conforming to Final cut (using integral frame rate value if frame drop flag is not set)
+ Overall bit rate mode (CBR or VBR)
+ DVB Subtitle: region_horizontal_address / region_vertical_address (hidden by default)
+ MPEG-TS: option (--ParseSpeed=0.8) for testing the stream until all advertised streams are detected
x MXF: some files with SDTI were not well demuxed (frame number) or were crashing
x Some files where wrongly detected as MPEG-PS without streams
x AC-3: non-48 KHz streams were not analyzed
x 3164893, JPEG: issue with some JPEG files with no/incorrect extention
x 3403338, crash with four character filenames

Version 0.7.48, 2011-08-16
--------------
+ ISM (ISM/ISMV/ISMA/PIFF...): more attributes (frame rate, frame count, duration)
+ MP3: support of VBRI header in a corrupted frame
+ Active Format Description: muxing mode (A/35 or SMPTE ST 2016-3)
+ MP4/MOV: Displaying info about first description atom of a track (instead of all of them)
x MP4/MOV: scan order (Top/Bottom) from container, thanks to Peter Chapman
x MP4: Channel layout was not filled in some cases
x libmms: default behavior is using official libmms API only
x Vorbis comment: album performer data was in performer field
x Captions in AVC: some captions were not detected and decoded
x AVC: GOP structure info was often wrong
x MXF: IndexTable search was buggy (parsing the whole file) for some rare files (index present twice)

Version 0.7.47, 2011-07-14
--------------
+ ISM (ISM/ISMV/ISMA/PIFF...) support
+ SCC (Scenarist Closed Captioning) support
+ #3201768, BMP: BitmapV4Header support, thanks to Lionel Duchateau
+ MP4: enda atom support (Endianess)
+ MXF: expliciting PCM endianess
x #3354384, Vorbis in AVI, version 3+ was displayed as 2+
x MPEG-4/MOV: files with big "free" atom were not parsed

Version 0.7.46, 2011-07-04
--------------
+ MXF: TrackName
+ DCP: Basic support (video and audio streams), both Interop and SMPTE versions
+ P2 Clip: Supporting directories with wrong letter case
+ MXF: detection of AES3/Dolby E in mono PCM streams without compression scheme
+ MP4: first frame duration difference if first frame has not the same duration than all other frames
+ MP4: frame rate set as CFR if only the first frame has a different duration
+ MP4: Handling of track duration smaller than media duration (track duration and stream size have priority)
+ #3348936, MXF: OP-Atom profile
+ Matroska, Stereo Layout support, thanks to Lionel Duchateau
x libcurl (FTP/HTTP): seek request was buggy, invalid data was read
x MPEG Video in MPEG-TS or MP4: streams were wrongly detected as VBR or CBR, any bit rate mode is removed until a working solution is found
x AES (S302M): was no more detected if the packet was split in several PES
x #3325235, Vorbis: streams were wrongly reported as CBR
x EXR: Width/Height were 1 less than real value
x #3344635, MP4/MOV: Delay missing if timecode track found before audio/video, thanks to Peter Chapman

Version 0.7.44, 2011-06-15
--------------
+ QuickTime: Support of EIA-608 streams created by Final Cut
+ Matroska: Support of Matroska tags
+ Matroska: parsing of tags at the end of the file (previously: only tags at the beginning)
+ MP4/MOV with uncompressed streams: scan type (interlaced/progressive)
+ YUV4MPEG2 (Y4M) support
+ GXF: more information about AES3 audio streams
+ EXR: Basic support
+ E-AC-3: dialnorm/dynrng (first frame / first block) information
+ LXF: support of DV PAL
+ DPX: Support of Cineon old format
+ DPX: Support of file with Little Endian configuration
+ MPEG-4: better support of file with more than one mdat or moov atom
+ WAV: more details from bext chunk
+ MXF: AFD (in both descriptor or ANC) support
+ MXF: Support of AES3 / Dolby E in 2 mono channels
+ MPEG-4: support of some malformed (without mdat atom) files
+ MXF: Better detection of streams without Essence Compression descriptor
x MPEG Video: Custom matrix detection was broken
x DPX: Industry specific header was not well decoded
x JPEG-2000: Chroma subsampling 4:2:0 was recognized as 4:4:4.
x Matroska: Handling default values for TrackVideoDisplayWidth and TrackVideoDisplayHeight
x MPEG-TS: private audio streams with 0xCO stream_id were not correctly detected
x FLV and RIFF: Changing Sorenson H.263 to Sorenson Spark because this stream format is too much incompatible with H.263
x MXF: Some specific files (including AFD in descriptor) were not well parsed

Version 0.7.44, 2011-04-19
--------------
+ Russian translation updated
+ AVI: support of some corrupted files (RIFF size set to 0)
+ AC-3: support of streams with 16-byte time stamp between frames
+ MPEG-TS: detecting wrong stream registration information and skipping it
+ NTSC: accepting 483-line streams as NTSC
+ MXF: Parsing speed improvement, especially for high bitrate (>300 Mbps) streams
+ TGA file basic support
+ libmms: MMSH support (Linux/Mac only), thanks to RGB Networks
+ libcurl: HTTP header option and Time to live, thanks to RGB Networks
+ Id3v2 and MPEG-4: possibility to customize tag mapping
+ TIFF: more details
x MXF: some specific files were not analyzed, fix sponsored by http://maxnine.com
x LXF: random crashes

Version 0.7.43, 2011-03-19
--------------
+ MPEG-TS: detection and display of real time PAT/PMT change improved
x EIA-708: crash with some specific files
x MP4: crash with wiles including mix of PCM and other audio formats
x AAC: crash with some malformed streams
x FLV: crash with some specific files (longer audio than video at the end of the file)
x MP4: wrong framerate information with some specific files (track header after media information)

Version 0.7.42, 2011-03-03
--------------
+ AES3: bit depth of AES3, bit depth of compressed audio stream, endianess
+ Id3v2 and MP4 tags: better mapping with iTunes fields
+ Tags: removal of technical only fields
+ MPEG Video: intra_dc_precision information (in advanced mode)
+ MXF: detection of raw pictures
+ MXF: support of URL encoded locators
+ MXF: color subsampling
+ DXW format support
x #3171468, ADIF: crash in some cases
x #3169916, IFO: crash in some cases
x #3173391, Id3v2: was using local encoding instead of ISO-8859-1
x MPEG Video, discarding bad (always 0) group_start
x AVC: crash with file having big log2_max_pic_order_cnt_lsb_minus4 value
x AVC: wrong GOP information in some cases
x Ut Video: Color space information was wrong
x MPEG-4 Visual: Advanced Simple@L0 was detected as Advanced Simple@L1
x AVI: Crash with some AVC streams (not sized blocks)
x MPEG Audio: crash with some files
x AAC: crash, infinite loop or wrong result with some files
x Trying to quicker reject a junk file
x AVI: OpenDML indexes were no more parsed

Version 0.7.41, 2011-01-24
--------------
+ MPEG-TS: random cases with full parsing of the file

Version 0.7.40, 2011-01-24
--------------
+ QuickTime: detection of AES3 and Dolby E
+ QuickTime: Apple CEA-608 and CEA-708 (in CDP) support
+ JPEG 2000: Chroma subsampling
+ JPEG 2000 in MXF: Color space
+ JPEG 2000 in MPEG-4 and MJ2: Color space
+ MPEG-4: mx5p CodecID support
+ ISO 9660 (CD/DVD/Blu-ray dump) detection
x MPEG-TS: wrong detection (as encrypted) of some streams with a lot of transmission errors
x MPEG-TS: wrong management of some PAT/PMT updates (real time parsing)
x MPEG Video: wrong duration with some raw streams
x FLV: wrong duration with some files
x libcurl: crash if requesting a libcurl URL but libcurl library not present
x MPEG Audio: crash with some malformed files
x MPEG-4: error between recorded date and encoded date

Version 0.7.39, 2011-01-03
--------------
+ OGG: more CELT attributes, thanks to Lionel Duchateau
+ DV: better detection of display aspect ratio for specific (non standard?) streams
+ MPEG-7: support of ISO-IEC 13818-2 / H.262 (MPEG Video) version 2000 (multi-view / 4:2:2)
+ MXF: Clean aperture size support
+ D-10 (AES3) in MXF: real channel count
+ MPEG-TS: detection and analysis of ADTS in stream_id_extension
+ #2943900, MPEG-4 Visual: count of maximum consecutive B-frames
+ MP4: Handling of Nero library information atom
+ Id3: TAG+ support
x #3140453, MKV with AAC: support of HE-AAC v1/v2 detection even if AudioSpecificConfig is missing
x #3138883, ID3v2: crash in some cases if Data length indicator is used
x #3139417, MPEG-PS: duration for caption detection was shorter than expected
x #3139276, H.263: more precision about the different flavors of H.263
x AVI and MPEG-2 Audio Layer 3: error if audio stream duration estimation
x FLV: better handling of weird bitrate metadata
x D-10 (AES3) in MXF: Bit rate was wrong
x AC-3: crash if false-positive detection of AC-3 and CRC is valid and bsid is wrongly set
x #3141059, FLV: trying to detect partial files (so metadata are wrong) and using timestamps instead
x #3141052, AVI: wrong duration, no detection of rec chunks, with some files
x #3145968, RealMedia: not integer frame rate handling + HE-AAC filling
x Id3v2: bad handling of some tags
x MPEG-TS/PS: Frame count / duration accuracy of some very specific files
x MPEG-TS/PS: Not counting non-decodable frames (without the corresponding I-Frame)

Version 0.7.38, 2010-12-16
--------------
+ uClibc compatibility, thanks to Metin KAYA <<EMAIL>>
+ MPEG-TS: ID in all streams if a TS streams contains several substreams
+ JPEG in MOV: Scan type
+ AAC: implicit SBR and implicit PS detection (complete HE-AAC and HE-AACv2 detection)
+ AAC/LATM: more details (channels, profile...)
+ AAC/SL: more details (channels, profile...)
+ MPEG-TS and MPEG-PS with trace feature activated (Linux/Mac by default): speed improvement
+ DTS: indication of HD and Core configurations if there is an HD part
+ #3118446, MXF: VC-3 (DNxHD) detection
+ VC-3 (DNxHD) raw stream support
+ VC-3 (DNxHD): Bit depth
+ MPEG-TS: option for ignoring ATSC transport_stream_id indication
+ Windows only, forcing output to UTF-8
x MPEG-TS: some teletext and DVB subtitles were not detected
x #3111584, MXF: phantom track with weird result if an unknown track is found
x #3116952, DTS and AC-3: was set as lossy in all cases (DTS Master Audio and AC-3 TrueHD are lossless)
x #3137160, ADTS: some files with wrong extension were not correctly detected

Version 0.7.37, 2010-11-22
--------------
+ DPX format support
+ Compression mode (Lossy / Lossless) information (note: JPEG 2000 "profile" is moved to this new field)
+ #3095129, AVI: MLC Lossless codec support
+ #3095136, AVI: AMV Video Codec support
+ IFO: chapter information (thanks to Bastian Wegener)
+ MPEG-4: header size / data size / footer size information (in advanced mode)
+ MPEG-4: AVmp CodecID (.mov referencing .mxf) support
x MXF: avoiding infinite loop due to circular reference

Version 0.7.36, 2010-10-24
--------------
+ HDV 720p/1080i/1080p commercial names support, sponsored by http://www.chyro.fr/
+ Raw TrueHD support
+ MP4: "lpcm" (PCM from DVD) Support (with channel positions)
+ MKV: compression mode (zlib...) display
+ WAV: support of AAC (ADTS) with CodecID 0x8180
+ WMV: Handling of weird Aspect Ratio information in Extended Content Description
+ #3087674, Ut Video Lossless support
+ #3087677, WebP: basic support (detection only)
+ #3072929, MP4: DTS Express support
+ MPEG-4: Handling of external files referenced by "code" atom
+ M-JPEG and M-JPEG 2000 are renamed JPEG and JPEG 2000
+ MP4 and HDV 1080i: detecting containers lying about width (saying 1920 but it is 1440)
+ DTVCC Transport and SCTE20: Option for displaying empty streams
+ DPX format detection
x #3034092, MPEG-TS: hang up if program is modified (zapping) in the file
x AAC: removal of empirical detection of SBR and PS (too many problems)
x AVI: trusting in priority frame count from index of indexes with broken files
x MKV: AVC "unknown profile" removal
x AVC: some frame rates were reported as twice the real frame rate
x #3029308, Id3v2: support of frames with Unsynchronisation bit set
x #3065114, MPEG-Video: duration calculation issues with raw streams and open GOPs
x AFD: crash with malformed streams
x MPEG-7 output: missing references for MPEG-4 Visual and RF64
x #3086701, ID3: freeze/crash with ID3 tag at the end of the the file
x LXF: some files were not completely analyzed (missing video)
x JPEG: Chroma subsampling value was always set to 4:2:0, wrong
x MP4 with MPEG Video: do not trust anymore raw stream timecode

Version 0.7.35, 2010-09-05
--------------
+ Arabic language added
+ Teletext subtitle support
+ DVB subtitle support
+ CEA-608/708: language
+ Library: "Per frame" interface, like ffmpeg
+ AFD: AFD (Active Format Description) value and detail
+ MPEG-TS: SCTE35 detection and parsing
+ MXF: Ancillary data (S436M) support
+ MPEG-TS: Codec ID (stream_type)
+ MXF parser improvements
x #3036119, MKV: default language value is eng
x DV: weird display with some DV with synch problems.
x AVC: wrong detection of bitrate mode for AVC in MP4
x MPEG-TS: crash and wrong detection of some non MPEG-TS files

Version 0.7.34, 2010-07-09
--------------
+ New icons set, thanks to Kevin Redon, based on Gnome themes: Neu, Experience, Gnome 2.18
+ WebM support, sponsored by http://digi-metrics.com/
+ LXF (Harris/Leitch) support
+ #3008704, IVF (Indeo Video Format) support
+ #3002376, Blu-ray: Source (.m2ts) for the .mpls parser
+ DV: handling of unsynched streams
+ Basic view, Bit Depth is added to the audio part
+ FLV: Framerate info for some not yet supported weird files
+ Demux (library only): demuxed packets can be forced to contain 1 complete frame
+ XML output: MediaInfo version
+ Parsing speed (Library only): option for a quicker analyzing, but with some missing features
+ AVI and Huffman (HFYU): Colorspace
+ AVI and Fraps: Colorspace
+ AVI and Lagarith: Colorspace
+ MPEG-4: Channel map basic support
x CEA-708: Skipping some malformed streams
x JPEG/M-JPEG: Resolution was in some random other field
x #3001707, AAC in MP4: mono aac file detected as stereo
x AVI: crash with some malformed files
x AVI: Stream identifier was not filled in some cases
x MPEG-PS: some files were not well detected
x WAV: Bit Depth is back
x WAV/PCM: Byte sign was wrong if Bit depth >8
x Decimal point issues with some specific compilers/OS
x MPEG-4: removing support of btrt atom for average bitrate, too many wrong values
x AVI: crash with some specific files (avc1 CodecID)
x MPEG-4+AAC: Trying to better detect the (non)presence of PS if there is no PS header
x MPEG-TS/PS: more precision on duration

Version 0.7.33, 2010-05-14
--------------
+ Slovak language added
+ Colorimetry field is replaced by Color space and Chroma subsampling
x Some word were not translatable
x Solaris port was broken

Version 0.7.32, 2010-05-02
--------------
+ MPEG-7 output improvements
+ DV: Better detection of DVCPRO 50 and 100 (again), especially in MXF
+ P2: support of P2 XML files and P2 directory structure
+ XDCAM: support of XDCAM XML files and XDCAM directory structure
+ N19: More attributes
+ ISO-639-2 3-letter standard for language field (for third-party software)
+ Additional commercial name of a stream or container (XDCAM IMX, DVCPRO, AVC-Intra 50...)
+ MXF: support of detection of files without Track number in the headers
+ MXF: Operational Pattern
+ MPEG-4: Aperture size support
x AVC: some files were wrongly analyzed (frame rate...)
x AVI: better support of files with only one big data chunk (professional cameras)
x MPEG-4: Huge memory usage with some files (files with DV and PCM)
x DVR-MS: width/height was wrong in previous version

Version 0.7.31, 2010-04-16
--------------
+ SCTE 20 closed caption support
+ Difference between width/height in the container and in the raw stream
+ AVC: GOP structure
+ MXF: more raw stream formats are detected (MPEG-4 Visual, A-law...)
+ DV: Better detection of DVCPRO 50 and 100
x DV: less files without the right extension wrongly detected as DV

Version 0.7.30, 2010-03-26
--------------
+ AVC: Multiview Video Coding (MVC) basic support
+ AVI: better handling of some RGB or RLE codecIDs
+ Solaris package creation files update (with relocation)
+ MSVB: better handling of C pointers
+ WAV: Skipping wrong sample count values
+ GXF: Caption in ancillary data detection
x #2970227, WAV: a file was wrongly detected as DTS
x CEA-608: some captions were wrongly detected as active
x MPEG-7: some corrections in order to be valid
x FLV: Duration for video not starting at Time 0
x WMV and MPEG-TS/PS: some crashes are fixed
x MSVC2010 and MSVC2008 project files were missing some files

Version 0.7.29, 2010-03-08
--------------
+ GXF (SMPTE 360M / RDD 14) support
+ GXF: support of CDP (SMPTE 334-2) in Ancillary data (SMPTE 334-1) (not complete)
+ AAC in MPEG-TS: Profile/Level
+ PCM in MKV: format name, channel positions
+ MPEG-7 and PBCore 1.2 output (pre-release)
+ MPEG-7: more metadata
+ Solaris package creation files
+ MPEG-TS overall bit rate is more precise
x Channel positions: more coherency in the naming
x DVD-Video (IFO): ID name was in hexadecimal only, now in Decimal+Hexa

Version 0.7.28, 2010-02-19
--------------
+ AIFF: ID3v2 tags support
+ AutoIt3: MediaInfoList interface
+ RIFF: Handling of INFO chunk in hdlr chunk
+ VorbisCom-based comments (FLAC, OGG...): support of new fields
+ MPEG-4 Visual: Duration for raw streams
+ Display Aspect Ratio 3:2 instead of 1.500
+ Detection of N19 (EBU Tech 3264)
+ MPEG-2 Video: new profiles (4:2:2, Multi-view)
+ MPEG-2 Video: GOP structure
+ MPEG-4: handling of video streams with a different last frame duration
+ RIFF: Skipping OpenDML frame count (not always valid), when index is available
+ AMR: more attributes (Sampling rate, duration...)
x MPEG-PS and MPEG-TS: crash with some files
x MPEG-4: some bitrates to zero with a null bitr or btrt atoms
x MPEG-4: managing different kinds of compressor name format
x AMR in MPEG-4: crash with some specific files
x #2952623, Id3v2: USLT with ISO-8859-1 charset was not well read
x #2952637, Id3v2.2: PIC tag was not well read
x #2952638, XML output: sometimes empty tags
x #2920138, XML output: invalid char in XML tags

Version 0.7.27, 2010-01-03
--------------
+ MPEG Audio: Emphasis
+ MPEG-TS: PCR timestamp can be extracted in real time
+ Linux library: visibility is now limited to the official API
+ License changed to LGPLv3+ for license simplification (same license as the library)
x MPEG Audio, AAC, Vorbis: removal of irrelevant Resolution field even from containers
x MPEG Video: error in calculation of frame rate if sequence extension is used
x MPEG Audio: some VBR files without VBR header were wrongly detected as CBR
x #2921999, unexpected quotes in the text output

Version 0.7.26, 2009-12-18
--------------
+ MXF: support of external files parsing
+ MLP support
+ TrueHD: more details (channels, sampling rate...)
+ TrueHD: display of both core and TrueHD details
+ #2905950, MPEG-4: Cover extraction
+ ADTS: better bitrate estimation
+ #2910579, MPEG-4 Visual: data_partitioned and reversible_vlc display
+ #2910572, MPEG Video: BVOP presence display
+ Internet Media Type (aka MIME-Type) display (but hidden by default)
+ ADTS: Id3v2 support
x #2897584, ID3v2: Crash with some malformed UTF-16 strings
x MXF: Better handling of interlaced content (height and frame rate)
x Homogenization of Video resolution (ie no more 24-bit resolution, but 8-bit)
x MPEG-TS: less false-positive detection of encrypted streams
x MXF: better management of complex files (with references)
x MOV: better management of complex files (with references)
x Digital Video: better management of DV50, more bitrate accuracy
x Digital Video: better management of DV100 (Width, Height, BitRate)
x Digital Video: Some Colorimetry (4:x:x) were wrong
x FLV: some files were not detected
x MPEG Audio, AAC, Vorbis: removal of irrelevant Resolution field
x MPEG formats: some Profile renaming, for more coherency

Version 0.7.25, 2009-11-13
--------------
+ MediaInfo (customized) with HTTP input: User Agent setting
+ #2896693, MPEG-TS: skipping some malformed PMT (with elementary_PID=0)
x #2844911, AAC in 3GP: false detection of SBR and PS (again)
x #2894411, MPEG Audio: UserDefined Covers replace other covers types
x MPEG Audio: Some profiles were not displayed
x RealMedia: Some Titles were not displayed
x Matroska: Wrong detection of 6-channel Wavpack (detected as 2-channel)

Version 0.7.24, 2009-10-30
--------------
+ MPEG-4: DVCPRO HD detection
+ WAV: better handling of Wave Extension codec IDs
+ MPEG Audio: profile and extension display
+ MPEG-TS: More information for ADTS in non-audio PES ID.
+ FLV: test of video bitrate info integrity
+ MPEG-4: "sbtl" subtitles support
+ MPEG-4: old-style Apple language codes support
+ MPEG-4: XDCAM support
+ MPEG Video: vbv_buffer_size info
+ AVC: cpb_size_value info
+ VC-1: hrd_buffer info
x #2882376, AVCHD: reports some incorrect AVCHD framerate
x MPEG-4: some Pixel Aspect Ratio settings were not handled
x Customized output: respect of carriage return from the platform
x MPEG-TS demuxer: more tolerance of bad blocks

Version 0.7.23, 2009-10-16
--------------
+ OGG: more details for MPEG-4 Visual, MPEG Audio and AC-3
+ MPEG-4: more information for MPEG Audio streams
+ MPEG-4: more information for ADTS streams
+ MPEG-TS: more information for ADTS streams
+ QCP (RFC 3625) format support
+ Refactoring of parsers, for more speed and less false-positives
+ WAV: INFO chunk parsing
x Blu-ray directory analyzing was broken
x VC-1: some streams were not detected
x AMR in MP4: Resolution set to 13 or 14 bits instead of default 16 bits
x Help, Known parameters was hanging up
x AVC: frame rate was wrong for progressive streams with frame doubling
x MPEG-TS: crash while parsing some streams with specific ATSC event_id

Version 0.7.22, 2009-09-25
--------------
+ MPEG-TS: KLV data detection
x #2859504, 3GPP: some files were not detected
x Some corrections about AVI Display Aspect Ratio
x QuickTime: external files were completely parsed, too long
x DV: Support of 4 mono channel configuration

Version 0.7.21, 2009-09-04
--------------
+ More permissive license for redistribution only
  Redistribution license is intended for companies
  with legal issues (if they can not deal with GPL)
+ AVC: Support of frame doubling/tripling
+ AVC: Colour description
+ QuickTime: support of external files parsing
x #2828430, Quicktime: 180 degrees rotation display was not displayed
x MPEG-TS: whole file was parsed (long)
x MPEG Video / MPEG-4 Visual: new algorithm for library name detection
x #2844911, AAC in 3GP: false detection of SBR and PS
x MKV: some chapters had timecode issues

Version 0.7.20, 2009-07-31
--------------
+ TAK format support, thanks to Lionel Duchateau
+ #2822681, Quicktime: Rotation display (from iPhone...)
+ ASP.net web application example
+ Java/JNA, Linux: dynamic load of libzen if not in LD_PATH
x MPEG-PS: Some durations were a bit too long
x MPEG-PS: Better handling of PTS/DTS for private and extension streams
x ADTS: Handling of SBR and/or PS streams

Version 0.7.19, 2009-07-17
--------------
+ ALS raw files support, thanks to Lionel Duchateau
+ LA (Lossless Audio, old) support, thanks to Lionel Duchateau
+ SLS detection, thanks to Lionel Duchateau
+ AAC: forcing SBR/PS when it is implicit (if <=24KHz, if 1 channel)
+ AVC: Closed captions detection
+ #2813919, APE: CompressionLevel
+ XML output: track names are changed, for a better usability by XML parsers
x MKV: Original display aspect ratio was not displayed
x #2817479, Blu-ray/M2Ts: crash with some specific file names
x MXF: the whole file was parsed, parsing now only the useful part

Version 0.7.18, 2009-07-03
--------------
+ MXF support
+ Blu-ray: BDMV directory parsing
+ Blu-ray: clpi/mpls files support
+ M2TS: Language if the .clpi blu-ray file is found
+ OpenSolaris support
+ MPEG-4 Visual: support of Sony SMC (security video) tags
+ MPEG-4 Visual: ASP profile was wrongly displayed
+ Mac OS X: Creation of universal and 10.4 compatible binaries option
x Mac OS X: no more double-carriage return in Text view

Version 0.7.17, 2009-06-19
--------------
+ #2790156, Folders context menu option
+ Python : adaptation for version 3
+ XML output (thanks to Steen Jost Jensen)
+ MPEG-PS/TS: ATSC Closed captions (both EIA-608 and EIA-708) detection
+ MPEG-PS/TS: Language of closed caption (ATSC)
+ DV: camera settings
+ DV: Closed captions detection (raw DV, or in .mp4 and AVI container)
+ JPEG 2000 support
+ MPEG-4: support of JPEG 2000 embedded in a MPEG-4 container
+ MPEG-4: support of ProRes 422 / ProRes 422 HQ codecs
+ MPEG-4, AMR: Vendor and version
+ Blu-ray: support of PCM (Frequency, resolution, bitrate)
+ MPEG-TS: speed improvements
x #2803396, sometimes crash with tiny (40-79 bytes) files
x #2801211, MPEG-4/3GP: support of 3GP tags
x #2795345, MPEG-4 Visual: crash with some specific user_data
x #2793960, MKV: some MKV renamed as .ac3 were detected as AC-3
x #2796417, MPEG-4: Some files with Camera user data were not detected
x #2785604, Build fails on newer GCC versions (>=4.4)
x #2664106, prefs overwritten after installing update
x so interface was not working since some releases on Linux
x MPEG Video: better detection of 2:3 Pulldown
x MPEG-4: Forcing AMR to 8KHz/Mono whatever the container says

Version 0.7.16, 2009-05-20
-------------
+ MPEG-4: More information for Avid DV streams
+ MPEG-4: more complete parsing (raw stream) of files with compressed headers
+ VC-1 and MPEG-Video: displaying the original framerate for streams with 2:3 pulldown
+ WMA: Handling of Mutual Excluded streams
x MPEG-4: some files with compressed headers were not parsed
x MPEG-4: Kodak files with EXIF data were not parsed
x MPEG-4 (Quicktime): some files with compressed header were not well parsed
x MPEG-4: Some files with a TimeCode track had wrong duration
x MPEG-PS: some files with wrong timestamp were not well parsed
x MPEG Audio with APE tags: crash with big tags

Version 0.7.15, 2009-04-30
-------------
+ Wave64 (.w64) support
+ MPEG-4: Better handling of 4GiB+ files
+ MPEG-4: audio parts from a DV stream are displayed
+ MPEG-4: better DV analyzing
+ DV: parser improvement (speed, accuracy)
x DTS: 768/1536 Kbps streams are corrected to the real bitrate (755/1510 Kbps)
x MPEG-TS: some stream durations were not well computed (few seconds only)
x AVC: freeze with some malformed encoder settings

Version 0.7.13, 2009-04-03
--------------
x Returned value has changed, back to the normal behavior (0 if it is OK)
+ #2635230, MPEG audio: Lame encoder settings
+ #2706146, WMV: detection of container wanted aspect ratio
+ #2611726, MKV: Audio delay detection
+ #2721811, MKV: TrueHD detection
+ Java: JNA (Java Native Access) binding
+ Exported "stream" interface for analyzing streams in memory (beta)
+ SHN (Shorten) detection
+ TAK (Tom's lossless audio compressor) detection
+ MPEG-TS parser optimizations
+ SWF: better handling of videos
+ WAV: better handling of 4GiB+ files
+ WAV: Extensible Wave support
+ MKV: Handling of chapters in multiple languages
+ Stream size for all streams when 1 video and all audio streams are CBR
+ Chapters are moved to menu part for better coherency with MPEG-TS, developers see Changes.txt for more info
x #2712761, AVI: unsupported SalmonSoft text codec makes MediaInfo silently fail
x #2719534, MKV: handling "modified" headers (zero padding)
x #2720822, MPEG Audio: was freezing with 1 malformed file
x #2721949, IFO: some stream IDs were false
x #2725808, MPEG-PS: some "OTA" files were detected with TrueHD instead of normal AC-3 audio

Version 0.7.12, 2009-03-20
--------------
+ Complete refactoring of the code for speed improvements and future features
+ #2686943, MPEG-4: ISMV (IIS Smooth Streaming Media Video) basic support
+ MPEG-4 Visual: distinction between MPEG and H.263 default matrices
+ MPEG-TS: ATSC and DVB EPG support
x MPEG-PS: duplicate stream infos with some AC-3 streams (with program map)

Version 0.7.11, 2009-02-13
--------------
+ OGG: Better Kate and CMML codecs support
+ AVI: Detecting wrong aspect ratio from a broken AVI header
+ DTS: Handling of Little Endian and 14-bit streams
+ WAV: ID3v2 tags parsing
+ MPEG Video: frame order even for progressive sequence
x #2559346, MKV: Detection of framerate in MKV without framerate header
x #2474280, OGG: some streams had bad stream size
x MPEG-TS: some buggy file may corrupt memory
x MP3/Id3v2: crash with one malformed file

Version 0.7.10, 2009-01-30
-------------
+ Spanish translation updated
+ Mono (C#/Linux) binding
+ MPEG-4: Encoding library name
+ #2474280, OGG: Old version of Dirac identifier support
+ MPEG-TS: Encoded date for HDV
+ DVR-MS: Delay between two tracks
+ WAV: EBU Broadcast Wave format v1 support
+ CMP4: Basic support
x #2529963: Infinite loop on one file
x #2473140, DTS: error preventing 96/24 DTS detection
x MPEG-4: Better support of Aspect ratio
x Output was unstranslated and weird when $LANG is not set
x C++ binding error in 0.7.9
x Python binding improved and corrected (no more crash with Linux)

Version 0.7.9, 2009-01-19
-------------
+ OGG: Dirac support
+ OGG: Speex support
+ OGG: other formats (JNG, Kate, MIDI, PCM...) detection
+ SWF: detection of more audio streams
+ MPEG-PS: Handling of PlayStation2 audio
+ #2474119, Minimal MZ (PE) and ELF detection
+ SMV (WAV/ADPCM+JPG files) file format support
+ DPG (Nintendo DS) file format support
+ QuickTime: TimeCode track analysis
+ MPEG-4 subtitles: difference between 'text' and 'tx3g' codecIDs
x Python binding update for more compatibilty
x #2474280, OGG: handling of files with Skeleton Metadata
x #2445654, OGG: better false-positives detection
x #2493685, AVI: Wrong video duration for some malformed 4GB+ AVI/DV files
x #2516007, "Language" raw tag was sometimes 3 letter long instead of 2-letter long
x Floating point overflow correction

Version 0.7.8, 2008-12-09
-------------
+ Changing version schema, only 3 numbers (next versions will 0.7.9, 0.7.10...)
+ Better Linux integration
+ rpm stuff (thanks to oc2pus http://packman.links2linux.org)
+ deb stuff
+ #2259421, FLAC and OGG: Encoding library name and version
+ FLV: version 10 support
+ FLV: duration of files without metatags
+ MPEG-4: Delay (useful when multiple videos are present)
+ Dirac: profiles updated from the latest specification
+ MPEG-4: more precise detection of OGG based codecs
+ MKV: better newest codecs handling
+ MKV: RealVideo/Audio support improvements
+ mipsel CPU support
x MPEG-PS: Trying to have a better precision for duration
x WMV: crash with some malformed files
x MPEG-TS: some channel names where not shown
x MPEG Video (version 2): some streams were misdetected as CBR

Version *******, 2008-11-10
---------------
+ #2219839, return code is now the count of analyzed files
+ You can now use a language file (ie --Language=file://en.csv)
x Language=raw is without ugly artifacts
+ #2216498, LPCM in VOB: more attributes (channels count, sampling rate, bitrate)
+ #2182135, Wavpack: support of multichannel files
+ AVC: detection of the newest profiles
+ DTS: detection of DTS-HRA, DTS-MA, DTS-Express, thanks to http://madshi.net
+ AES3 PCM in MPEG-TS detection
+ Wavpack: Encoder settings
+ WMV with "WVC1" codec identifier: video interlacement
+ MPEG-4: E-AC-3 support
x #2186682, MPEG-2 Video: Wrong PAR and DAR for Panasonic MPEG2 Files
x MPEG-4: some AC-3 and AAC specific files reported wrong channels value

Version *******, 2008-10-17
---------------
+ Support of VP5 file format
+ MPEG-4 Visual and MPEG Video: can now display the custom matrices data
+ WM: interlacement detection for VC-1 based codecs (WMV3...)
+ #2148321, PNG: more info
+ AVI: Better association of abcAVI tags to MediaInfo tags
+ PureBasic binding
+ Delphi binding: dynamic load (by default) of the library, thanks to Icebob
x #2142995, MPEG-4: handling the Display Aspect Ratio from the track Header (tkhd)
x #2141277, MPEG-PS/TS: Audio ID was hex for MPEG-PS but decimal for MPEG-TS streams
x #2109107, Tags in Flac or Ogg: modification of the tag types priorities
x #2120224, MPEG-4: some specific files were not parsed completely (missing info)
x MPEG-TS with Dirac: some specific files were not parsed completely (missing info)
x MPEG-4: Crash on MPEG-4 Visual format without DecDecoder stuff
x Command line: was not reading custom template in UTF-8 codepage
x DV: some files were not well detected
x MPEG Video: some files were badly detected with 3:2 pulldown
x AVS (Chinese): some corrections, thanks to Tom's translation of documents

Version *******, 2008-09-12
---------------
+ A small doc for helping automatic parsing
+ [2088009] Flac: Picture tag support
+ Flac: Support of Id3v1&2 in a Flac file
+ WM (ASF/WMV/WMA): detection of wrong framerate in header, trusting now the timestamp instead of the header
+ WM (ASF/WMV/WMA): detection of framerate from the timestamp when the framerate info is not available in the header
+ MPEG-4 with AVC: Added information about buggy files readable by all players except iPods
+ OGG: OGG with FLAC (pre- and post-FLAC 1.1.1) support
x FLI/FLC: were not correctly detected
x Output=HTML was not working
x MPEG-TS without PAT/PMT: the complete stream was parsed, may be very long
x Wavpack: duration and bitrate were false
x #2071681, MPEG Audio: handling of truncated MPEG audio frames at the end of a file
x #2032760, MPEG Audio: handling of MPEG Audio files with junk at the end
x #2085149, Id3v2: was crashing with some malformed tags

Version *******, 2008-08-22
---------------
+ Chinese Simplified, Italian, Korean, Russian, Albanian languages updated
+ #2044174, AVS (Chinese Audio Video Standard) support
+ #2030684, AVI: Support of malformed chunks
+ Matroska: delay between audio and video calculation (for AC-3, DTS, MPEG Audio)
+ Full Replay gain support (gain/peak, track/album) for Flac/Ogg/MP3
+ MPEG-TS without PAT/PMT (example: stream captured by some satellite TV receivers) support
+ MPEG-TS: Service info (name, provider, channel number) for both DVB and ATSC tags
+ Dot and thousand separator localized
+ Matroska: Handling of audio Delay
x #2023872, DV: Crash with floating point exception on some files
x #2024706, BDAV: Some QuickTime files misidentified as BDAV
x #2033307, MPEG Video: Some raw files with high bitrate were not detected
x #2040411, Id3v2: Crash with some malformed Id3v2 fields
x #2036580, Id3v2: Problem when parsing big (>1M) Id3v2 tags, file was no more detected
x #1893830, WMA/WMV: there was textual info (Linux/Mac only)
x Video, Resolution/bits per pixel was sometimes per color entity, sometimes per pixel, now always per pixel
x MPEG Video, the "Component" video standard was not detected
x DLL only, the "by buffer" interface was broken

Version 0.7.7.4, 2008-07-11
---------------
+ #1995653, AVI: Delay of interleaves (example: "64 ms (1.53 video frames)")
+ #1995574, AVI: Report if the audio frames are split across or aligned on
+ MPEG-Video (raw): handling of "not started at the beginning" time_code
+ MPEG-PS: Trying to prevent errors with time code reset in the stream
+ AVC: Trying to detect raw AVC streams without SPS/PPS
+ [2013746] AAC in MPEG-TS: complete parsing of ADTS stream for more info about AAC
+ AAC in MPEG-TS: complete parsing of IOD based stream for more info about AAC
+ MPEG-TS: better detection of PCR time code
+ AAC: specifying the muxing mode (ADIF, ADTS or LATM)
+ MediaInfo DLL: NetBeans java binding example

Version *******, 2008-06-27
---------------
+ MPEG-TS: Detection of scrambled streams
+ #1995566: AVi, Identify ODML files
+ AC-3/DTS/AAC: Added another Channel configuration string (example: "3/2.1") in advanced mode
+ #1995569: Added proportion of each stream in the file (example: "500 MiB (90%)")
x MPEG-TS: Handling of multiple programs in one PMT PID
x MPEG-TS: Handling of streams in multiple programs

Version *******, 2008-06-13
---------------
+ FLV: detection of AVC and AAC
x #1981032, Visual C++ 2008 binary is not compatible with Win9X, coming back to Visual C++ 2005
x #1964299, Never-ending 100% CPU if input file doesn't exist

Version *******, 2008-05-30
---------------
+ MPEG Video: Detection of 2:3 and 2:2:2:2:2:2:2:2:2:2:2:3 Pulldown
+ VC-1: Detection of 2:3 and 2:2:2:2:2:2:2:2:2:2:2:3 Pulldown
+ AVC: Detection of MBAFF
+ Translations: more words can be translated
+ MPEG-4 files: More info (profile, format settings, encoder name, x264 settings) for some well-known stream formats
+ MPEG-4 files: Better handling of not common formats (ALS, PNG, AC-3, DTS... in .mp4)
+ Bluray: detection of subtitles
+ Additional formats detected: Module, Extended module, Impulse Tracker, Scream Tracker 3
x #1967663, mp4: some specific iTunes atoms were not correctly parsed

Version *******, 2008-05-16
---------------
+ Python (Windows) binding
+ AC-3 TrueHD in bluray detection (even if the stream is splitted in 2 sub-streams)
+ Original framerate is shown if the framerate from the video stream and the one in the container are different
+ #1955061, Bluray: Duration
+ #1953908, FLV with audio delay detection
x Linux with UTF-8 locale was not accepting non-English characters in filenames
x #1954663, Some mono HE-AAC audio track detected as 2 channels with PS
x VC-1 in TS detection even if the stream_type is not declared
x xxxBSD compatibility

Version 0.7.6.4, 2008-04-27
---------------
+ About the library : Java/Linux binding is available
+ DV (raw) files support
+ Better support of DV in AVI container (type 1 and 2)
+ AVC: Handling of SEI which is before a SPS
+ AVC in AVI: reading more info (Profile, Cabac...) from streams created by FFMpeg
+ Vorbis in AVI: reading more info (Max bitrate, tags...) from streams created by FFMpeg
+ Detection of VC-1 in MPEG-TS from MainConcept encoder
+ MediaInfoLib is now thread-safe
x -f option was not working in the last version
x #1943743, AVI with MPEG-Audio VBR and delay: delay was incorrect
x Pixel Aspect Ratio was sometimes inverted (1/x)
x XviD CodecID was redirected on Koepi website, no more available, changed to official XviD webpage
x #1946098, DV in AVI: DV aspect ratio errors

Version *******, 2008-04-12
---------------
+ AVC: Count of reference frames
+ AU: Comments
+ Some global speed improvement
x #1931844, Id3v2: Unsynchronized frames support (v2.3 & v2.4)

Version *******, 2008-03-28
---------------
+ Spanish translation updated
+ The ToolTip text is now a summary (instead of a complete list)
+ MKV: handling of files created for streaming
+ MPEG-4: Handling of corrupted stream size info with some PCM streams
+ DVD video: Hebrew patch ("iw" code is mapped to Hebrew)
x MPEG-4: better handling of bitrate mode (VBR or CBR)
x AVI: MediaInfo reads now the framerate value from the container rather than the stream value
x #1931844, Id3v2: Unsynchronized frames support (v2.3 & v2.4)

Version *******, 2008-03-14
---------------
+ AC3: TrueHD detection
+ MPEG-TS (or Bluray): VC-1, AC3+ and AC3 TrueHD detection
+ AVC in MKV: all SEI userdata infos from x264/eavc in Writing library settings
+ EVO: Better detection of duration
+ Dirac: raw files parsing
+ MPEG-TS: Dirac management
+ MPEG-TS: DVB subtitles/Teletext management
x MPEG-4 Visual (DivX/XviD): writing library name was missing
x MPEG-Video: some DTS files with wrong extension were detected as MPEG-Video
x SWF: Compressed SWF files support is back
x "Inform" option with file as parameter was not well parsed

Version 0.7.6.0, 2008-02-29
---------------
+ Id3v2.2 (yes, it is old...) support
+ Id3v2: TXXX tags support
+ RMP3 (japanese MP3 based format?) support
+ SAMR in .mp4: some files have wrong sampling rate, forcing it to 8000 Hz
+ File_Created_Date and File_Modified_Date tags added (in advanced mode only)
+ MP3 encoded by Lame: Minimal (VBR) or Nominal (ABR) bitrate
x Better detection of Lame encoder
x AVC Display Aspect Ratio was not well detected if AVC is in a .mp4 container
x Matroska Display Aspect Ratio was deleted if AVC aspect ratio was set, now Matroska has priority
x "Subwoofer" term has been replaced by "LFE", more relevant

Version 0.7.5.9, 2008-02-08
---------------
+ AVC: CABAC detection
+ #1886241: AVC: all SEI userdata infos from x264/eavc in Writing library settings
+ ADPCM: compagny which created the stream is now shown in the codec section
+ PCM: precision about Little/Big endianess, Integer/Floating point method
+ #1882691: AC3+: raw files are now detected
x AVC: FPS were sometimes 2x the reality
x RealMedia parser was brocken
x FLV parser was crashing
x FLV: Better detection of Nellymoser audio
x FLV: Disabling Framerate estimation if a VFR file is detected (estimation was false in this case)
x Better false-positive MP3 detection (NSV files are no more detected as MP3)
x IFO: there was a mistake betwwen NTSC and PAL framerates
x Win9X support was (again) brocken in 0.7.5.8

Version 0.7.5.8, 2008-02-01
---------------
+ Matroska: Full parsing of DivX/XviD/H264/AVC settings (profile...)
+ AVI: Full parsing of AVC settings (profile...)
+ AVI: Added a tag "OpenDML" (for example, the PlayStation 3 doesn't support OpenDML files)
+ AVC: Framerate, Interlacement, writing library (only for eavc, x264 and MainConcept encoders)
+ TwinVQ : channels, bitrate and sampling rate
+ Musepack SV8 support
+ Musepack and Musepack SV8: tags support
+ AIFF and AIFC format support
+ MIDI format detection
+ FLV: FrameRate even if the tag is not in the header
+ AU/SND audio formats support
+ Vodei encrypted AVI files detection
+ Linux/MacOS: support of 2GiB+ files
+ FLV: Video parsing even if the container says there is no video
x APE Tags v1 are detected again
x MPEG-PS: some malformed files were not well parsed, fixed
x AVC: Cropping was not well handled (example: height was 1088 instead of 1080)

Version *******, 2008-01-09
---------------
+ SWF: Tags version 9 support
+ MPEG-TS: DegiCipher 2 (DCII) support
+ DVR-MS format support (with MPEG Audio or AC3)
+ WMV: language tag
+ MPEG-2 Video: Nominal bitrate
x AVI: some speed improvments
x Output was bad with some files

Version *******, 2007-12-10
---------------
+ AVI, Interlacement for M-JPEG codec
+ AVI, Interleaved tag
+ Better showing of well know values ("44.1 KHz", "22.05 KHz"...)
x AVI, was parsing the whole file in case of non-interleaved files
x MP3, was freezing on specific files
x #1838202: Flac, Resolution was 1 less the the real value (15 bits instead of 16...)

Version *******, 2007-11-09
---------------
+ #1799859: Flac, tags are case insensitive
+ Matroska format parsing improvements
+ .mp4 speed improvement
x BDAV (Bluray) parser compilation was forgotten in the previous version
x AVI, Bitrate and stream size were false
x #1825218: IFO parser was brocken
x #1825521: .mp4 chapters times were wrong
x #1801549: DLL was crashing during Delphi/C# debugging sessions

Version *******, 2007-11-02
---------------
+ Aspect Ratio is renamed Display Aspect Ratio
+ Pixel Aspect Ratio added (in advanced mode)
+ More attributes for AC3+
+ More attributes for VC-1
+ Profile and level for MPEG-4 Visual based formats (DivX...), AVC and VC1
+ Speed improvement for MPEG-TS, MPEG-PS, DVD-Video (.vob), HD-DVD, BluRay
+ MPEG-4 AAC, Channels position
+ AAC (ADIF), More info
+ AAC (ADTS), More info
x FLV, there was bad dates on some specific metatags.
x #1818404: WAV with "float" PCM, bad detection
x CDXA (.dat) files parsing is back

Version 0.7.5.3, 2007-10-08
---------------
+ Better handling of non-Latin characters in the command line
+ Better OGG/OGM parsing
+ Better .ifo (DVD-Video) parsing
+ .rmvb files are now detected in Windows Explorer
+ Win9X/98/Me support does not request Microsoft unicows.lib anymore
x Win95/98/Me support was brocken since *******. It works again.
x #1798997: MPEG-4, some Apple codecs were wrongly reported
x Lot of memory leak correction, you can now parse 1000+ files without problems

Version 0.7.5.2, 2007-08-16
---------------
x Some crashes on Linux PPC with specific files
x #1770477: Video info for some low-bitrate MPEG files were not shown
x #1770509: MPEG 2 Audio Layer 2 was not well detected
x #1763282: FLV, wrong playtime on PPC based machine
x #1734113: IFO, crash with some specific files

Version *******, 2007-07-23
---------------
+ AVI: 24/30 fps (aka 120 fps hack or dual frame rate video) detection
+ TruAudio (.tta) support
+ Wavpack (.wv) support
+ MPEG-4 based containers: Codec/CC tag support
+ #1754143: AC3 & DTS detection in a .mp4 container made with the Haali's gdsmux tool
+ #1754140: AC3 detection in a .mp4 container from Nero Digital
+ #1754905: iPhone files support
x #1750275: HD-DTS was no more detected in a .m2ts (bluray) file
x AVI: AAC profile name was no more detected

Version *******, 2007-07-09
---------------
+ Licence changed from GPLv2 to GPLv3
+ More information about mpeg-4 based (Nero...) files
+ MediaInfo analyses longer a .vob file to be able to find hidden tracks (subtitles...)
+ #1747633: Encrypted WMV detection
+ FLV: more attributes
x Memory leaks correction
x #1720404: MPEG, rare crashes with some files
x #1721846: AAC ADTS were no more detected
x MPEG Audio: No detection if there is some specific junk before the real audio data
x #1728059: Delphi DLL overflow with big files
x Crash with Youtube FLV files
x AVI: Delay for AC3, DTS and MP3 is back

Version *******, 2007-05-14
---------------
+ OGG Vorbis, "Floor" value (useful for some players which don't support Floor 0)
+ MPEG-Audio, Lyrics3v1/v2, ApeTagv1/v2 parsing
+ MPEG-TS, handling of complex files (multiple programs, 8+ channels...)
+ #1704008: handling of AVC or MPEG-4 Video in a MPEG-TS file without program map
+ VC-1 basic parsing
+ DTS True HD detection
+ AC3+ detection
+ HD-DVD (decyphered) support
+ BluRay (decyphered) support
x MPEG-TS, Duration calculation problem on very rare files
x MPEG-4/iTunes, Tags where not well parsed in *******
x FLV, freezes or crashes with some files
x #1718269: MPEG-4 Video, Framerate of 65535/2733 is transformed to 23.976
x Windows Media (ASF/WMA/WMV) with file size more than 4GB were not well handled
x MP4 with file size more than 4GB were not well handled

Version *******, 2007-04-28
---------------
+ Windows 64-bit version of the command line tool and DLL (separately)
+ Linux 64-bit version of the command line tool (separately)
+ More Musepack properties
+ Detection of Musepack v8 files
+ Complete parsing of Real Media files (all tags)
+ Improved Windows Media (ASF/WMA/WMV) parsing
+ MonkeyAudio tags (APETagv1/v2, Id3v1) parsing
+ Ogg/Theora : Video bitrate
+ MPEG audio : More encoder detection (bitstream parsing)
+ SWF (Flash) parsing
x #1665981: Mov - PCM audio (digital camera) was detected as RGB
x #1672896: Wave file with .mp3 extension was detected as MP3
x #1689570 and #1633237: Better JPEG parsing
x Some other minor bugs

Version *******, 2007-02-23
---------------
+ Program file size reduction
+ WAV : Support of some special tags at the end of the file
+ AVC : Display Aspect Ratio
x 1653325: Wrong Resolution with AVC High Profile
x 1662890: Problem with tag adaptation parser (mainly for ID3v1 genre)
x Better support of corrupted MOV files

Version ******* 2007-02-05
---------------
+ You can now use your own templates (take a look on /plugins/custom/ directory)
+ FLV format support
+ Basic support of multiple programs in a MPEG Transport Stream (Satellite broadcast)
+ Basic detection of TwinVQ (.vqf) files (+tag parsing)
+ Better handling of corrupted .mp4 files
+ Better handling of corrupted .avi files
x Crash with corrupted "COMM" Id3v2 tags
x Crash with some audio files
x 1633524: MOV, Crash on files with corrupted Descriptors
x 1630907: MP3, Crash on files with corrupted (empty) Id3v2 tag
x 1637838, 1634549, 1635131: Crash with some audio-only files
x AVI, Better detection of writing application found in a "JUNK" chunk
x AVI, sometimes wrong playtime
x 1637191: AC3 delay in AVIs is back
x 1635134: some freezes with MPEG-7 files (but this format is not yet supported)
x 1635087: some files were wrongly detected as AC3
x Russian (or other non-latin language) AVI tag parsing is back.

Version ******* 2006-12-22
---------------
+ Using of templates for output
+ MPEG-TS : Detect encrypted stream, and show the codec
+ id3v2 with UTF-16 frames
+ AVC in MPEG-TS AVC parsing
+ AVC : Width and Height
+ MacIntel version
x Matroska, some crashing files (with chapters)

Version ******* 2006-12-09
---------------
+ Czech language
+ Encoders database updated with 20 new encoder versions
+ Codecs database updated with 100 new codecs (video or audio)
x Matroska, sometimes wrong Playtime
x MPEG-4 : some Titles were not well parsed
x MPEG PS : some video stream were missing
x Crash with some malformed files
x Some debug files were created

Version 0.7.4.1 2006-12-08
---------------
+ MPEG Transport Stream : Video info (Standard, Chroma, Interlacement...) and PlayTime
+ CDXA : PlayTime
+ MPEG-4 Video embedded in MPEG Program Stream support
+ MPEG-1 stream embedded in a Quictime file
+ MPEG-4 AAC Parametric Stereo (SBR-PS) detection
+ Detection of encrypted MPEG-4 (like iTunes)
+ Support of MPEG TS with lot of synchro errors
+ AVI : Exact Stream size calculation (but currently OpenDML files are not supported)
+ MPEG-4 : Exact Stream size calculation
+ Basic support of Korean mobilephone provider Sky (.skm)
x MPEG Program Stream : PlayTime, Delay are corrected
x #1601787 : MOV tracks issues (and crash)
x MOV : was stopping on too big "free" atoms
x MOV : Aspect Ration of DV is corrected
x MPEG : Handling of negative Delays between Audio and Video
x Musepack parser was doing too much "false positives" with some MPEG files

Version 0.7.4.0 2006-11-24
---------------
+ Linux i386 version
+ AVI with AAC-SBR : detection of real SamplingRate (not the sampling rate in header)
+ AVI with DV : basic information about audio
+ AVI with DV : detection of duplicated audio stream
+ Google Video : metadatas
+ AVI : now able to read metadatas at the end of the file
+ AVI : Bitrate computed is based on filesize in header instead of real filesize (for broked files)
+ AVI (DivX) : Menu detection (yes or no)
+ AVI : detection of VBR MP3 (useful for DVD players, sometimes they don't accept it)
+ AVI : detection of Delay between Audio and Video (for MPEG Audio, AC3 and DTS)
+ PlayStationPortable MPEG-4 files support
+ 3GPP5 files support
+ MPEG-4 : Detection of MP3, Vorbis streams embeded in a 'mp4a' atom
+ MPEG-4 : Detection of AVC streams embeded in a 'mp4v' atom
+ MPEG-2TS with MPEG-4 and AC3 detection
+ Speed improvements
x #1551482 "\n" in filename get replaced by newline in "Text" output
x AVI : infinite loop on one example file
x AVI : some bad detections corrected
x MPEG-PS : infinite loop in intra Matrix on one example file
x MPEG TS : better detection of streams

Version 0.7.3.1 2006-07-30
---------------
+ MPEG-1/2 PS : more files have a duration
+ AVI with MPEG-4 Video based codecs (DivX, XviD...), more encoder string detections
x Matroska, crash with big (>4GB) files

Version 0.7.3.0 2006-07-19
---------------
+ MPEG-1 and 2 Transport Stream support
+ MPEG-1 and 2 Video : Custom Matrix detection
+ MPEG-4 Video : Custom Matrix detection
+ Google Video (GVI) support
+ AMV/MTV (Chinese) detection (if you have specifications of theses formats, email me)
+ AVI/DivX/GoogleVideo and MPEG-4 Video : packed bistream detection.
  Now you can know if your DVD player can read the file before burning. Next step: a database of DVD player capacities ;-)
+ MPEG-4 : Language
+ MPEG-4 : Text streams (subtitles)
x #1485003 : MP3, "Title" tag was sometimes forgotten
x #1485804 : Wrong bitrate and time in PCM files
x #1485810 : wrong playtime on CBR MP3 files with big id3v2 tags
x #1485803 : some WMA tags were forgotten
x #1488449 : crash on very rare buggy MP3 files
x #1482686 : ShellExtension, crash when moving a directory
x #1488770 : PlayTime, some "minutes" formating were forgotten
x #1485044 : some .ico files were detected as MPEG Audio
x AVI, handle some malformed timestamps in tags (carriage return at the end...)
x MPEG-1 and 2 : incoherancy of PlayTime

Version 0.7.2.1 2005-05-08
---------------
+ MPEG-4 : Chroma ("4:2:0"...)
+ MPEG-4 : Interlacement mode ("Top field first", "bottom field first", or "Progressive")
+ MPEG-2 : Chroma ("4:2:0"...)
+ MPEG-2 : Interlacement mode ("Top field first", "bottom field first", or "Progressive")
+ MPEG-2 : Profile and Level ("Main@Main"...)
+ MPEG-2 : Standard (NTSC, PAL, SECAM, or MAC)
x #1482346 : Incorrect playback time in OGG files (was not enough precise).
x #1482602 : MPEG-4 video false positive files
x "lang=raw" option is back

Version 0.7.2.0 2005-04-30
---------------
+ AVI with DivX or XviD : encoder name, GMC/QPel/B-frames indicator if present (beta)
+ AVI and WAV with MP3 : encoder name, precise version and layer number, VBR indication
+ Matroska: support of anamorphic videos (right Aspect Ration even if pixel aspect ratio is not 1:1)
+ AVI/WAV : support of extended wave files (20 bit per sample and more than 2 channels) and channel position
+ AVI : support of Exif tag format
+ AVI : more tags are supported
+ Real : detect RealAudio Lossless Format
x #1454010 : v0.7.1.2 was checking updates even if the user did not want
x #1454009 : AC3, sometimes wrong AC3 playtime
x #1469423 : MP3 with cyrilic filenames, Id3 tags can be read
x #1464066 : Hang ups with raw h264 streams
x x1462647 : Quicktime files with preview image, crash
x x1445150 : crash with some malformed MusePack files (workaround, this is a the Musepack bug, not yet corrected)

Version 0.7.1.2 2005-03-10
---------------
+ #1441664: (for developpers using CLI) you can now have internal names of each tag ("-lang=raw")
+ #1438441: show TV format (PAL/NTSC) for DVD and DV
+ #1343147: Matroska, Show Chapter names
+ MPEG-1 and 2, more precise playtime
+ MPEG-1 and 2, Added Encoded_Application if it is in video user data
+ Quicktime: Support of Quicktime "wide" (header at the end of the file)
+ MPEG Audio: Detect small (examples, less than 200K) MPEG Audio files with Id3v1 tag
+ #1441661: AVI, Handling of DV Type 1
x #1440480: No Audio Found in MPEG-2 after 0.7.0.4
x #1438987: Infinite loop with some Matroska files
x Matroska, Recorded date is now in UTC format, rather than the number of seconds since 1970
x AC3 noted 5 channels (because of 5.1) are now noted 6 channels (more realistic)
x Quicktime: "ima4" or "twos" codecs were detected as Video stream instead of Audio stream

Version 0.7.1.1 2005-02-24
---------------
+ Now ~900 codec names (Audio/video/Text) are known (instead of 500)
+ Matroska : Framerate is now for all kinds of codec (and not only FourCC based ones)
+ MPEG-4 (m4V, Quicktime...) with unicode filename are now handled (before : crash on htis kind of file)
+ #1326802: MOV with 3GPP datas.
+ Meta-tags of Quickime HD files
+ #1244215: bit-rate details for MPEG-1 Video (VBR) (calculated with other CBR streams)
+ Detection of MXF files
+ #785338: Huge work on RealMedia parser (bitrate, channels, sampling rate, fps...)
x #1435086: Translation problems if the program is launched directly with a filename
x #1361647: Complete rewrite of MPEG-4 (newest Quicktime, 3GPP, iTunes...) parser to be more robust.
x Some minor (but crashing :( ) bug fixes
x AVI with DV was not well parsed

Version 0.7.1.0 2006-02-02
---------------
+ Speed improvements
+ #1383832 : support of last version of Quicktime files
+ Support of Musepack files
+ Support of Flic (FLI/FLV) video files
+ Support of DTS-HD (DTS at 96 KHz / 24 bits)
+ Support of ID3 tags (v1 and v2) in .AAC files
+ Support of all Tags in AVI files
+ Detection of QuickTime image files and QuickTime compressed archives
+ Detection of AVC files (3GPP, 3GPP Mobile or JVT)
+ Detection of iTunes protected files
+ Detection of H264 files (Raw, 3GPP or JVT)
+ Detection of Dirac raw video files
+ Detection of TrueAudio files
+ Detection of WavePack files
+ Support of files with more than 8 audio streams or 32 text streams
x #1420672 : No WMV bitrate
x Crash with some corrupted MPEG files
x Crash with some MP3 files with corrupted ID3v2 tags
x Crash with some Quicktime files with corrupted tags
x #1257550 (again :) ) : with AVI, rounding of FPS had an impact of FrameCount, based on FPS. Now based on AVI file (better ;-) )
x #1381652 : Reported some GIF files as Mpeg audio
x #1381619 : UPX compressor is no more used, to prevent Virus checker to use 100% CPU
x 2nd Video framerate forgotten
x Video languages were not parsed (example : "en" should be "English")
x FrameRate/String was forgeting measure (example : "at 25" should be "at 25 fps")
x Some case sensitive problems (example : "KBps" instead of "Kbps", 8 times more)
x DTS and AC3 channels position names were changed to be more human readable

Version 0.7.0.3 2005-11-14
---------------
NOTE : this version is released with urgency, because of Kaspersky anti-virus false-positive detection. It should be compatible with MediaInfo 0.6.1.1 (mainly for AutoGK compatibility), but I don't promise all works!
+ Uses MediaInfoLib 0.7.0.3
+ Text : URL for Text codecs
+ Support of ISO-639 language names (more than 200 language names can be translated)
+ Support of "multilanguage" language name (ISO-639 "mul")
x #1323208 : Video Bitrate for OGM files with VBR audio was incorrect
+ Better detection and handling of DivX container
+ #1327902 : More meta data properties for RIFF files
x #1327468 : RIFF (AVI) files with not-padded (INFO) sub chunks can't be parsed
+ More "generic" tags added
x Some other minor bugs
x #1257550 : rounding of FPS was 2 digit precise, not enougth, upgraded to 3 digits, and exact number is given (23.97 before, 23.970 or 23.976 now)
x Better false positive detection (mainly in MPEG1, MPEG2, MP3)
x some memory leaks if you use MediaInfo a long time
x MPEG Audio : VBR bitrate corrected for MPEG **1** streams
x MPEG Audio : VBR bitrate corrected for monochannel streams
x #1274999 : mp3Pro timing was wrong (2x the real timing)
+ "BitRate_Mode" with MP3 (VBR or CBR)
+ "PlayTime" with AC3
+ "PlayTime" with WAV

Version 0.6.1.1 2005-06-24
---------------
New
- Uses MediaInfoLib 0.6.1.1

Warning :
APE parser has some problem with ANSI compilation, I disabled it in this release.
Hope to have it again in the next release.

Version 0.5.2. 2005-
---------------
New
- Uses MediaInfoLib 0.5.2.0
- Begin of compilation under Linux : Core, and OGG format

Version 0.5.0.2 2005-01-11
---------------
New
- Uses MediaInfoLib 0.5.1.0

Bug fixes
- Some problems with CreateFile : if CreateFile doesn't work, I use WxFile instead
- MPEG Video and MPEG Audio : bug between version 0.4 and version 0.5 corrected
- PlayTime Calculation for OGM was false

Version ******* 2005-01-11
---------------
New
- Uses MediaInfoLib *******

Bug fixes
- Win95/Win98/WinMe support again! (problem with Unicode and AVI)
- AVI files were not well released (file locked)
- Better MPEG4 support
- Better coherency tests : no more file with 10 MP3 streams ;-)

Version ******* 2004-01-10
---------------
New :
- Using MediaInfoLib *******

Bug fixes :
- "-f" doesn't need to be in last position any more
- Can really analyse multiple files on command line

Version ******* 2003-08-01
---------------
Bug fixes
Using MediaInfoLib *******
